.main {
  align-items: center;
  justify-content: center;
  display: flex;
  padding: 10px;
}
.container {
  /* padding: 40px; */
  align-items: center;
  justify-content: center;
  min-height: 90vh;
  background-color: var(--color-background);
}
.inner {
  align-items: center;
  justify-content: center;
  display: flex;
  align-self: center;
}

.heading {
  text-align: center;
  margin-bottom: 40px;
  font-size: 28px;
  padding: 40px 0px 0px;
}
.backButton {
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
  padding: 20px 0px 0px;
}

.backButton:hover {
  color: var(--color-text-primary);
}
.card {
  border-radius: 12px;
  transition: all 0.3s ease;
  background: var(--color-card) !important;
  cursor: pointer;
  transition: all 0.3s ease;
  /* width: 50%; */
}

:global(html[data-theme='dark']) .activeCard .price,
:global(html[data-theme='dark']) .activeCard .month {
  color: black;
}

:global(html[data-theme='dark']) .card {
  background-color: rgb(77, 102, 138) !important;
}
:global(html[data-theme='light']) .card {
  background-color: #edeff1 !important;
}
:global(html[data-theme='dark']) .activeCard {
  background-color: #bafbbc !important;
  box-shadow: 0 0 8px rgb(9, 187, 95);
  color: black;
}
:global(html[data-theme='light']) .activeCard {
  background-color: #bafbbc !important;
  box-shadow: 0 0 8px rgb(9, 187, 95);
  color: black;
}
.card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(116, 150, 202, 0.3) !important;
  /* box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); */
}

.price {
  font-size: 24px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  font-weight: bold;
}

:global(html[data-theme='dark']) .price {
  color: #fff;
}
:global(html[data-theme='dark']) .month {
  color: #fff;
}

.month {
  font-size: 16px;
  padding-left: 5px;
  color: var(--color-text-tertiary);
  font-weight: 200;
}
.title {
  font-size: 24px;
}
.description {
  font-size: 16px;
  /* margin-bottom: 16px; */
}

.plansSection {
  height: 100%;
  background: transparent;
  border: none;
  border-radius: 0;
}

.planCard {
  border-radius: 8px;
  background: var(--color-card) !important;
  text-align: center;
  height: 100%;
  /* border: 1px solid #e5e7eb; */
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.planCard:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px #22c55e;
  /* border-color: #22c55e !important; */
}

.highlightCard {
  background-color: #bafbbc !important;
  /* border-color: #22c55e !important; */
}

:global(html[data-theme='dark']) .highlightCard {
  background-color: #bafbbc !important;
}
:global(html[data-theme='light']) .highlightCard {
  background-color: #bafbbc !important;
}
.planTitleHightlight,
.priceHightlight {
  color: #000 !important;
}

.planTitle {
  font-weight: 600;
  font-size: 16px;
  color: var(--color-text-primary);
  display: block;
  margin-bottom: 6px;
}

.price {
  font-size: 24px;
  font-weight: 700;
  margin: 6px 0 3px 0 !important;
  color: #000;
}

.perMonth {
  font-size: 12px;
  font-weight: 400;
  color: var(--color-text-primary);
}
.perMonthHightlighted,
.planDescriptionHightlighted {
  color: #000 !important;
}

.planDescription {
  font-size: 12px;
  color: var(--color-text-primary);
  display: block;
  margin-top: 6px;
}

@media (max-width: 900px) {
  .title {
    font-size: 20px;
  }
  .description {
    font-size: 14px;
  }
  .month {
    font-size: 14px;
  }

  .price {
    font-size: 21px;
  }
}
