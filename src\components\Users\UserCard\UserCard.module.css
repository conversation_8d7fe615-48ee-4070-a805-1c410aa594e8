.userCard {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  box-shadow: 3.12px 7.79px 40.53px 0px rgba(0, 0, 0, 0.09);
  background-color: #fff;
  border-radius: 20px;
  padding: 20px;
  position: relative;
}
:global(html[data-theme='dark']) .userCard {
  background-color: rgb(77, 102, 138) !important;
}
.userCardImg {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

.name {
  font-size: 18px !important;
  font-family: var(--font-radio-canada) !important;
  margin-top: 15px;
  color: #000;
  font-weight: 500;
}

.role {
  font-size: 14px !important;
  font-family: var(--font-poppins) !important;
  color: var(--color-text-primary);
  font-weight: 300;
  margin-top: -5px;
  opacity: 0.6;
}
.roleStatus {
  font-size: 12px !important;
  font-family: var(--font-poppins) !important;
  color: var(--color-text-primary);
  font-weight: 300;
  margin-top: -5px;
  opacity: 0.6;
}
.avatarContainer {
  position: relative;
}

.editIconOuterContainer {
  background-color: #fff;
  border-radius: 50%;
  padding: 3px;
  position: absolute;
  right: 5px;
  bottom: 5px;
}

.editIconContainer {
  background-color: #8edafe;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  border-radius: 50%;
}

.editIcon {
  cursor: pointer !important;
  font-size: 18px !important;
  color: #fff;
}

.closeIconContainer {
  cursor: pointer !important;
  background-color: #ff3939;
  border-radius: 50%;
  padding: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 10px;
  right: 10px;
}

.closeIcon {
  cursor: pointer !important;
  font-size: 20px !important;
  color: #fff;
}

.actionButton {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

@media (max-width: 600px) {
  .name {
    font-size: 16px !important;
  }
}
