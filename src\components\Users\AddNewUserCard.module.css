/* CreateNewFolderCard CSS Module */

.styledCard {
  border-radius: 12px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  background-color: var(--color-white) !important;
  box-shadow: 3.12px 7.79px 40.53px 0px rgba(0, 0, 0, 0.09) !important;
  height: 100% !important;
}

html[data-theme='light'] .styledCard {
  background-color: var(--color-white) !important;
}

html[data-theme='dark'] .styledCard {
  background-color: transparent !important;
  box-shadow: 0;
  border: 1px solid #ffffff !important;
}

.styledCard:hover {
  transform: translateY(-2px) !important;
}

.styledCard :global(.ant-card-body) {
  padding: 16px !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
}

.plusIcon {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  background-color: #4bae4f;
}

html[data-theme='dark'] .styledCard:hover .plusIcon {
  background-color: transparent;
}

html[data-theme='light'] .styledCard .plusIcon {
  border-color: var(--color-primary);
}

html[data-theme='dark'] .styledCard .plusIcon {
  border-color: var(--color-white);
}

.styledCard:hover .plusIcon .anticon {
  color: white !important;
  background-color: var(--color-white) !important;
}

.createText {
  font-size: 18px !important;
  font-family: var(--font-radio-canada) !important;
  color: var(--color-text-primary);
  font-weight: 500 !important;
}

.memberText {
  font-size: 14px !important;
  font-family: var(--font-poppins) !important;
  color: var(--color-text-primary);
  font-weight: 300;
  opacity: 0.6;
}

html[data-theme='light'] .createText {
  color: var(--color-primary);
}

html[data-theme='dark'] .createText {
  color: var(--color-white);
}

html[data-theme='light'] .styledCard:hover .createText {
  color: var(--color-primary);
}

html[data-theme='dark'] .styledCard:hover .createText {
  color: var(--color-white);
}

@media (max-width: 600px) {
  .createText {
    font-size: 16px !important;
  }
  .plusIcon {
    width: 75px;
    height: 75px;
  }
}
