'use client';
import { Form, FormInstance } from 'antd';
import { useEffect, useState } from 'react';
import { isMobile, isTablet } from 'react-device-detect';
import { Endpoints } from '../../config/endpoints';
import siteConfig from '../../config/site.config';
import { getApiData } from '../../helpers/ApiHelper';
import { useAppSelector } from '../../store/hooks';
import styles from './Interests.module.css';

const { useWatch } = Form;

type Interest = {
  _id: string;
  interest_name: string;
  interest_image?: string;
  status: string;
};

type Props = {
  form: FormInstance;
};

export default function InterestsPage({ form }: Props) {
  const [interests, setInterests] = useState<Interest[]>([]);
  const { user } = useAppSelector(state => state.auth);
  const selectedInterests = useWatch('interests', form) || [];

  useEffect(() => {
    if (user?.interests && user.interests.length > 0) {
      form.setFieldValue('interests', user.interests);
    }
  }, [user, form]);

  useEffect(() => {
    const fetchInterests = async () => {
      try {
        const response = await getApiData<
          // eslint-disable-next-line @typescript-eslint/no-empty-object-type
          {},
          { status: boolean; data: Interest[] }
        >({
          url: `${siteConfig.apiUrl}${Endpoints.interestList}`,
          method: 'GET',
          customUrl: true,
        });

        if (response && response.status === true && response.data) {
          setInterests(response.data);
        }
      } catch (error) {
        console.log('Error fetching interests:', error);
      } finally {
      }
    };

    fetchInterests();
  }, []);

  const toggleInterest = (interestId: string) => {
    const updated = selectedInterests.includes(interestId)
      ? selectedInterests.filter((i: string) => i !== interestId)
      : [...selectedInterests, interestId];
    form.setFieldsValue({ interests: updated });
  };

  return (
    <Form form={form} layout='vertical'>
      <Form.Item name='interests' hidden />
      <div className={styles.container}>
        <div className={styles.tags}>
          {interests.map((item, index) => {
            const isSelected = selectedInterests.includes(item._id);
            return (
              <div
                key={index}
                className={`${styles.tag} ${isSelected ? styles.selected : ''}`}
                onClick={() => toggleInterest(item._id)}
                style={{
                  cursor: isMobile || isTablet ? 'default' : 'pointer',
                }}
              >
                {item.interest_name}
              </div>
            );
          })}
        </div>
      </div>
    </Form>
  );
}
