.container {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.userName {
  font-weight: 500 !important;
  font-size: 18px !important;
  font-family: var(--font-radio-canada) !important;
  color: #000000 !important;
  margin-bottom: 5px !important;
}

:global(html[data-theme='dark']) .userName {
  color: #fff !important;
}

:global(html[data-theme='dark']) .userEmail {
  color: #edeff1 !important;
}

.userEmail {
  font-weight: 400 !important;
  font-size: 14px !important;
  font-family: var(--font-poppins) !important;
  color: #555555 !important;

  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.userInfo h3 {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.userInfo p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.activeTag {
  margin-left: 15px;
  font-size: 14px;
  border-radius: 20px;
  padding: 2px 10px;
}

.backBtn {
  background: #001f54 !important;
  border: none;
  border-radius: 8px;
  font-weight: 500;
}

/* Column Headers Styles */
.columnHeaders {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-left: 40px;
  margin-top: 30px;
  position: relative;
}

.headerRow {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 20px;
}

:global(html[data-theme='dark']) .headerRow {
  background: transparent !important;
}

.headerDate {
  width: 15%;
  /* padding-left: 5px; */
}

.headerFolder {
  width: 40%;
  text-align: left;
  padding-left: 20px;
}

.headerStatus {
  width: 15%;
  text-align: center;
  padding-right: 20px;
}

.headerRole {
  width: 25%;
  text-align: center;
  padding-right: 30px;
}

.columnTitle {
  font-weight: 400 !important;
  font-size: 16px !important;
  font-family: var(--font-poppins) !important;
  color: #000000 !important;
}

:global(html[data-theme='dark']) .columnTitle {
  color: #fff !important;
}

/* Timeline Container */
.timeline {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 25px;
  margin-left: 40px;
  padding-top: 20px;
}

/* Vertical line */
.timeline::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -10px;
  width: 2px;
  height: 120%;
  background: #d9d9d9;
}

/* Timeline item */
.timelineItem {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
}

/* Icon wrapper */
.iconWrapper {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: relative;
}

/* Small triangle pointer */
.triangle {
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 8px solid #fff;
}

.statusTag {
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  border: none !important;
  align-self: flex-start;
}

.statusTagDraft {
  background-color: #faad14 !important;
  color: white !important;
}

.statusTagSealed {
  background-color: #52c41a !important;
  color: white !important;
}

.statusTagInReview {
  background-color: #0059ff !important;
  color: white !important;
}

.statusTagDefault {
  background-color: #d9d9d9 !important;
  color: #666 !important;
}

/* Colors */
.orange {
  background: #fa8c16;
}
.purple {
  background: #722ed1;
}
.green {
  background: #52c41a;
}
.red {
  background: #ff4d4f;
}
.gray {
  background: #8c8c8c;
}

.role {
  font-weight: 500 !important;
  font-size: 14px !important;
  font-family: var(--font-poppins) !important;
  color: #000000 !important;
}

/* Content box */
.content {
  flex: 1;
  background: #ffff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  box-shadow: 2.29px 5.72px 29.74px rgba(0, 0, 0, 0.09);
  padding-right: 30px;
  padding-left: 20px;
  padding-bottom: 15px;
  padding-top: 15px;
}

:global(html[data-theme='dark']) .content {
  background: #8edafe;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}

.backButton {
  padding: 0;
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
  margin-bottom: 10px;
}

.avatar {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50%;
}

.date {
  font-weight: 300;
  color: #555555;
  font-family: var(--font-poppins);
  font-size: 13px;
  text-overflow: ellipsis;
  overflow: hidden;
}

:global(html[data-theme='dark']) .date {
  color: #012458;
}

:global(html[data-theme='dark']) .folder {
  color: #012458;
}

:global(html[data-theme='dark']) .role {
  color: #012458;
}

.folder {
  font-weight: 500;
  font-family: var(--font-radio-canada);
  color: #000;
  font-size: 16px;
}

.roleSelectDrp {
  min-width: 100px;
  cursor: pointer !important;
}

.roleSelectDrp :global(.ant-select-selector) {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.roleSelectDrp :global(.ant-select-selection-item) {
  color: #000000 !important;
  font-weight: 400;
  font-size: 16px;
  font-family: var(--font-poppins) !important;
}

.roleSelectDrp :global(.ant-select-arrow) {
  color: #000000 !important;
  font-size: 16px !important;
}

.revokeBtn {
  border-radius: 8px !important;
  font-weight: 500;
}

.icon {
  font-size: 30px;
  color: #fff;
}

:global(html[data-theme='dark']) .titleText {
  color: #fff !important;
}

.titleText {
  font-size: 16px !important;
  text-align: center !important;
  font-family: var(--font-poppins) !important;
  font-weight: 500 !important;
  color: #000000 !important;
}

.text {
  font-size: 18px !important;
  text-align: center !important;
  font-family: var(--font-poppins) !important;
  font-weight: 500 !important;
  color: #000000 !important;
}

:global(html[data-theme='dark']) .text {
  color: #fff !important;
}

/* Responsive adjustments for column headers */

@media (max-width: 1500px) {
  .headerFolder {
    padding-left: 10px;
  }
}

@media (max-width: 1200px) {
  .columnHeaders {
    display: none;
  }

  .iconWrapper {
    width: 40px;
    height: 40px;
  }

  .icon {
    font-size: 20px;
    color: #fff;
  }
  .timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -10px;
    width: 2px;
    height: 100%;
    background: #d9d9d9;
  }
}

@media (max-width: 900px) {
  .avatar {
    width: 50px !important;
    height: 50px !important;
  }
  .timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -16px;
    width: 2px;
    height: 100%;
    background: #d9d9d9;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
  }
}
@media (max-width: 600px) {
  .timeline {
    padding-top: 20px;
  }

  .timelineItem {
    gap: 8px;
  }

  .userName {
    font-size: 16px !important;
    margin-bottom: 0px !important;
  }

  .userEmail {
    font-size: 12px !important;
  }

  .userInfo {
    gap: 10px;
  }

  .timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -18px;
    width: 2px;
    height: 100%;
    background: #d9d9d9;
  }

  .avatar {
    width: 45px !important;
    height: 45px !important;
  }

  .titleText {
    font-size: 14px !important;
  }

  .text {
    font-size: 16px !important;
  }
}
