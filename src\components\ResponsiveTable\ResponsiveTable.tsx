/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import type { TableColumnType, TableProps } from 'antd';
import { Card, Empty, Table, Typography } from 'antd';
import React from 'react';
import useMediaQuery from '../../hooks/useMediaQuery';
import styles from './ResponsiveTable.module.css';

const { Text } = Typography;

interface ResponsiveTableProps<T extends Record<string, any>>
  extends Omit<TableProps<T>, 'columns'> {
  columns?: TableColumnType<T>[];
  mobileCardTitle?: (record: T) => React.ReactNode;
  mobileCardSubtitle?: (record: T) => React.ReactNode;
}

function ResponsiveTable<T extends Record<string, any>>({
  dataSource,
  columns,
  ...rest
}: ResponsiveTableProps<T>) {
  const isMobile = useMediaQuery('(max-width: 768px)');

  if (isMobile && dataSource) {
    return (
      <div className={styles.mobileContainer}>
        {dataSource?.length > 0 ? (
          dataSource.map((record: T, index: number) => (
            <Card
              key={
                rest.rowKey
                  ? typeof rest.rowKey === 'function'
                    ? rest.rowKey(record, index)
                    : record[rest.rowKey as keyof T]
                  : index
              }
              size='small'
              className={styles.mobileCard}
              bordered={true}
            >
              <div className={styles.cardBody}>
                {columns?.map((col: TableColumnType<T>) => {
                  const columnKey =
                    col.key?.toString() || (col.dataIndex as string) || '';

                  const getValue = (): any => {
                    if (col.dataIndex) {
                      if (Array.isArray(col.dataIndex)) {
                        return col.dataIndex.reduce(
                          (obj: any, key: string | number) => obj?.[key],
                          record
                        );
                      } else {
                        return record[col.dataIndex as keyof T];
                      }
                    }
                    return undefined;
                  };

                  const value = getValue();

                  return (
                    <div key={columnKey} className={styles.cardRow}>
                      <Text className={styles.tableHeader}>
                        {col.title as React.ReactNode}
                      </Text>
                      <Text className={styles.cardValue}>
                        {col.render
                          ? (col.render(
                              value,
                              record,
                              index
                            ) as React.ReactNode)
                          : (value as React.ReactNode)}
                      </Text>
                    </div>
                  );
                })}
              </div>
            </Card>
          ))
        ) : (
          <Empty />
        )}
      </div>
    );
  }

  // Default desktop table
  return (
    <Table<T>
      dataSource={dataSource}
      columns={columns}
      {...rest}
      className={styles.table}
      rowHoverable={false}
      locale={{
        emptyText: <Empty />,
      }}
    />
  );
}

export default ResponsiveTable;
