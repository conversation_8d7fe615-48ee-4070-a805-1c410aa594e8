import { useNotification } from '@/components/Notification/NotificationContext';
import { images } from '@/config/images';
import { useTranslation } from '@/hooks/useTranslation';
import { downloadFromInternalApi } from '@/utils/CommonFunctions';
import { Button, Divider, Typography } from 'antd';
import dayjs from 'dayjs';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';
import { isMobile } from 'react-device-detect';
import { PaymentStateProps } from '../PaymentStatus/PaymentStatus';
import styles from './PaymentReceipt.module.css';

const { Title, Text } = Typography;

const PaymentReceipt = ({
  type = 'success',
  data,
}: {
  type: 'success' | 'failed';
  data: PaymentStateProps;
}) => {
  const router = useRouter();
  const pathName = usePathname();
  const { t } = useTranslation();
  const notification = useNotification();
  const fromSubscription = pathName.includes('/subscription');
  const fromIdeas = pathName.includes('/my-ideas');
  const [loader, setLoader] = useState(false);

  // const handleDownloadReciept = async () => {
  //   try {
  //     const res = await getApiData<{ transaction_id: string }, ApiResponse>({
  //       url: `${Endpoints.downloadReceipt}?transaction_id=${data?.transaction_id}`,
  //       method: 'GET',
  //       data: {
  //         transaction_id: data?.transaction_id,
  //       },
  //     }); // your GET API
  //     if (res?.status === true && res?.data) {
  //       window.open(res?.data?.url, '_blank');
  //     } else {
  //       notification.error({
  //         message: res?.message || 'Error downloading receipt',
  //       });
  //     }
  //   } catch (error) {
  //     notification.error({
  //       message: 'Download Failed',
  //       description: 'Failed to download the receipt. Please try again.',
  //     });
  //     console.error('Download failed:', error);
  //   }
  // };

  const downloadCall = async () => {
    if (data?.receiptUrl) {
      setLoader(true);
      let res = { status: false, message: '' };
      res = await downloadFromInternalApi(data.receiptUrl);
      // if (fromSubscription) {
      // } else {
      //   res = await downloadFile(data.receiptUrl);
      // }
      if (!res?.status) {
        notification.error({
          message: res?.message || 'Error downloading receipt',
        });
      }
      setLoader(false);
    } else {
      notification.error({
        message: 'Receipt URL not found',
      });
    }
  };

  const endDate =
    data?.next_billing_on &&
    dayjs.unix(Number(data?.next_billing_on)).format('MMMM D, YYYY hh:mm A');

  return (
    <div className={styles.container}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          width: '100%',
          justifyContent: 'center',
        }}
      >
        <Title level={1} className={styles.pageTitle}>
          {t('receipt.title')}
        </Title>
      </div>

      <div className={styles.content}>
        <div className={styles.iconSection}>
          <Image
            src={images.receipt}
            alt='Receipt'
            width={isMobile ? 60 : 100}
            height={isMobile ? 90 : 150}
          />
        </div>
        <div className={styles.textSection}>
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.date')}</Text>
            <Text className={styles.propText}>
              {dayjs(data?.issueDate).format('MMM DD, YYYY')}
            </Text>
          </div>
          <Divider className={styles.divider} />
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.amount')}</Text>
            <Text className={styles.propText}>${data?.total}</Text>
          </div>
          <Divider className={styles.divider} />
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.plan')}</Text>
            <Text className={styles.propText}>{data?.planName || '-'}</Text>
          </div>
          <Divider className={styles.divider} />
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.invoice')}</Text>
            <Text className={styles.propText}>{data?.invoiceId || '-'}</Text>
          </div>
          <Divider className={styles.divider} />
          {fromSubscription && (
            <div className={styles.propSection}>
              <Text className={styles.propTitle}>
                {t('receipt.next_billing_date')}
              </Text>
              <Text className={styles.propText}>{endDate || '-'}</Text>
            </div>
          )}
        </div>
      </div>
      <div className={styles.buttonSection}>
        <Button
          type='primary'
          style={{ width: '100%' }}
          loading={loader}
          onClick={async () => {
            downloadCall();
            // window.open(data.receiptUrl, '_blank');
          }}
        >
          {t('receipt.download_pdf')}
        </Button>
        <Button
          type='default'
          style={{ width: '100%' }}
          onClick={() => {
            if (fromIdeas) {
              router.replace('/my-ideas');
              return;
            }
            if (fromSubscription) {
              router.replace('/subscription');
              return;
            }
            router.replace('/dashboard');
          }}
        >
          {t(
            fromSubscription
              ? 'receipt.back_to_subscription'
              : 'receipt.back_to_idea'
          )}
        </Button>
      </div>
    </div>
  );
};

export default PaymentReceipt;
