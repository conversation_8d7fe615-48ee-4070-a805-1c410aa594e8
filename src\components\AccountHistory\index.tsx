'use client';

import { Endpoints } from '@/config/endpoints';
import { getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { ArrowLeftOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Pagination, Spin, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { formatDate } from '../../utils/CommonFunctions';
import styles from './accountHistory.module.css';

const { Title, Text } = Typography;

interface HistoryItem {
  date: string;
  action: string;
  description: string;
  by_user: string;
}

interface ActivityLogParams {
  page: number;
  searchVal?: string;
}

interface ApiPagination {
  totalCount: number;
  pageSize: number;
  totalPage: number;
  currentPage: number;
  isMore: boolean;
}

interface ActivityLogApiRecord {
  action: string;
  description: string;
  date: string;
  by_user: string; // note the space
}

interface ActivityLogResponse {
  status: boolean;
  data: ActivityLogApiRecord[];
  pagination: ApiPagination;
}

interface ApiError {
  status: false;
  message: string;
}

export default function AccountHistory() {
  const router = useRouter();
  const { t } = useTranslation();

  const [data, setData] = useState<HistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');

  const fetchData = async (pageNum: number, searchVal?: string) => {
    setLoading(true);
    try {
      const payload: ActivityLogParams = { page: pageNum };
      if (searchVal && searchVal.trim() !== '') {
        payload.searchVal = searchVal.trim();
      }

      const res = await getApiData<
        ActivityLogParams,
        ActivityLogResponse | ApiError
      >({
        url: Endpoints.activityLogs,
        method: 'POST',
        data: payload,
      });

      if (res && 'data' in res && res.status) {
        setData(res.data);
        setTotal(res.pagination?.totalCount || 0);
        setPageSize(res.pagination?.pageSize || 10);
      } else {
        setData([]);
        setTotal(0);
      }
    } catch (err) {
      console.error('❌ Error fetching account history:', err);
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // Call API whenever page or search changes
  useEffect(() => {
    fetchData(page, search);
  }, [page, search]);

  return (
    <div className={styles.container}>
      <Button
        type='link'
        icon={<ArrowLeftOutlined />}
        className={styles.backButton}
        onClick={() => router.replace('/settings')}
      >
        {t('common.back')}
      </Button>

      <Title level={2} className={styles.heading}>
        {t('settings.activity_history')}
      </Title>
      <Text className={styles.subheading}>
        {t('accountHistory.track_your_account')}
      </Text>

      <div className={styles.searchWrapper}>
        <Input
          placeholder={`${t('accountHistory.search_activity')}...`}
          prefix={<SearchOutlined />}
          className={styles.searchInput}
          value={search}
          onChange={e => {
            setSearch(e.target.value);
            setPage(1); // reset to first page when searching
          }}
          allowClear
        />
      </div>

      {loading ? (
        <div className={styles.loadingWrapper}>
          <Spin size='large' />
        </div>
      ) : (
        <>
          <div className={styles.table}>
            <div className={`${styles.row} ${styles.headerRow}`}>
              <Text className={styles.col}>
                {t('accountHistory.date_time')}
              </Text>
              <Text className={styles.col}>{t('accountHistory.action')}</Text>
              <Text className={styles.col}>
                {t('accountHistory.description')}
              </Text>
              <Text className={styles.col}>{t('accountHistory.by_user')}</Text>
            </div>

            {data.length > 0 ? (
              data.map((item, index) => (
                <div key={index} className={styles.row}>
                  <Text className={styles.col}>
                    <span className={styles.mobileLabel}>
                      {t('accountHistory.date_time')}:{' '}
                    </span>
                    {formatDate(item.date).utc} UTC
                    <br />
                    {formatDate(item.date).local} LT
                  </Text>
                  <Text className={styles.col}>
                    <span className={styles.mobileLabel}>
                      {t('accountHistory.action')}:{' '}
                    </span>
                    {item.action}
                  </Text>
                  <Text className={styles.col}>
                    <span className={styles.mobileLabel}>
                      {t('accountHistory.description')}:{' '}
                    </span>
                    {item.description}
                  </Text>
                  <Text className={styles.col}>
                    <span className={styles.mobileLabel}>
                      {t('accountHistory.by_user')}:{' '}
                    </span>
                    {item.by_user}
                  </Text>
                </div>
              ))
            ) : (
              <div className={styles.empty}>
                {t('accountHistory.no_activity_found')}
              </div>
            )}
          </div>

          {total > pageSize && data.length > 0 && (
            <div className={styles.loadMoreWrapper}>
              <Pagination
                current={page}
                pageSize={pageSize}
                total={total}
                onChange={p => setPage(p)}
                showSizeChanger={false}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
