'use client';
import { SearchOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Input,
  Modal,
  Pagination,
  Row,
  TableColumnType,
  Typography,
} from 'antd';
import { debounce } from 'lodash-es';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react';
import { Endpoints } from '../../config/endpoints';
import { images } from '../../config/images';
import { ApiResponse, getApiData } from '../../helpers/ApiHelper';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useTranslation } from '../../hooks/useTranslation';
import InviteMemberModal from '../modals/InviteMemberModal';
import { useNotification } from '../Notification/NotificationContext';
import ResponsiveTable from '../ResponsiveTable/ResponsiveTable';
import styles from './UserManagement.module.css';

const { Title, Text } = Typography;

interface User {
  id: string;
  owner_id: string;
  user_id: string;
  folder_id: string;
  fullname: string;
  email: string;
  joined_folder_count: number;
  profile_pic: string;
}

interface paginationData {
  totalCount: number | null;
  pageSize: number;
  totalPage: number | null;
  currentPage: number;
  isMore: boolean;
}

interface UserResponse extends ApiResponse {
  data: User[];
  pagination: {
    currentPage?: number;
    isMore?: boolean;
    pageSize?: number;
    totalCount?: number | null;
    totalPage?: number | null;
  };
}

const UserManagement: React.FC = () => {
  const router = useRouter();

  const { t } = useTranslation();

  const notification = useNotification();
  const [open, setOpen] = useState(false);
  const [usersListData, setUsersListData] = useState<User[]>([]);
  const [pagination, setPagination] = useState<paginationData>({
    currentPage: 1,
    isMore: false,
    pageSize: 10,
    totalCount: null,
    totalPage: null,
  });

  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(false);

  const [openConfirmModal, setOpenConfirmModal] = useState(false);
  const [removeId, setRemoveId] = useState('');
  const [removeLoading, setRemoveLoading] = useState(false);

  useEffect(() => {
    getUsersList(1, searchValue, true);
  }, []);

  const getUsersList = async (
    page?: number,
    searchVal?: string,
    load?: boolean
  ) => {
    try {
      if (load) {
        setLoading(true);
      }
      const res = await getApiData<
        { page?: number; searchVal?: string },
        UserResponse
      >({
        url: Endpoints.usersList,
        method: 'POST',
        data: {
          page: page || 1,
          searchVal,
        },
      });
      if (res && res.status === true) {
        if (Array.isArray(res.data)) {
          setUsersListData(res.data);
          if (res.pagination) {
            setPagination({
              currentPage: res.pagination.currentPage ?? 1,
              isMore: res.pagination.isMore ?? false,
              pageSize: res.pagination.pageSize ?? 10,
              totalCount: res.pagination.totalCount ?? null,
              totalPage: res.pagination.totalPage ?? null,
            });
          }
        }
        setLoading(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.log('Error fetching user data:', error);
      setLoading(false);
    }
  };

  const removeUser = async () => {
    try {
      setRemoveLoading(true);
      const res = await getApiData<{ remove_user_id: string }, ApiResponse>({
        url: Endpoints.removeUserFromList,
        method: 'POST',
        data: {
          remove_user_id: removeId,
        },
      });
      if (res && res.status === true) {
        setRemoveLoading(false);
        notification.success({
          message: 'Success',
          description: res?.message || 'User removed successfully',
        });
        setOpenConfirmModal(false);
        setRemoveId('');
        getUsersList(pagination?.currentPage, searchValue);
      } else {
        setRemoveLoading(false);
        notification.error({
          message: 'Error',
          description: res?.message || 'Something went wrong',
        });
      }
    } catch (error) {
      setRemoveLoading(false);
      notification.error({
        message: 'Error',
        description: error?.message || 'Something went wrong',
      });
    }
  };

  const columns: TableColumnType<User>[] = [
    {
      title: t('userManagement.usernameEmail'),
      dataIndex: 'fullname',
      key: 'fullname',
      render: (_: unknown, record: User) => (
        <div className={styles.userInfo}>
          <Image
            src={record?.profile_pic || images.userPlaceholder}
            alt={record.fullname}
            width={40}
            height={40}
            className={styles.avatar}
          />
          <div className={styles.userText}>
            <Text className={styles.userName}>{record.fullname}</Text>
            <Text className={styles.userEmail}>{record.email}</Text>
          </div>
        </div>
      ),
    },

    {
      title: t('userManagement.joinedFolder'),
      dataIndex: 'joined_folder_count',
      key: 'joined_folder_count',
      render: (_: unknown, record: User) => (
        <Text className={styles.count}>{record.joined_folder_count}</Text>
      ),
    },

    {
      title: t('userManagement.action'),
      key: 'action',
      render: (_: unknown, record: User) => {
        return (
          <a
            onClick={e => {
              e.stopPropagation();
              setOpenConfirmModal(true);
              setRemoveId(record.user_id);
            }}
            className={styles.remove}
          >
            {t('userManagement.remove')}
          </a>
        );
      },
    },
  ];

  const sm = useMediaQuery('(max-width: 600px)');
  const xxs = useMediaQuery('(max-width: 400px)');

  const debouncedSearch = useCallback(
    debounce(value => {
      getUsersList(1, value, true);
    }, 500),
    []
  );
  interface dataProps {
    email: string;
    role: string;
    folder_id?: string;
  }
  interface sendInviteProps {
    status: boolean;
    message: string;
    data: object;
  }
  const handleSendInvite = async (data: dataProps) => {
    setLoading(true);
    try {
      setLoading(true);
      const sendData = {
        access_role: data?.role || '',
        email: data?.email || '',
        folder_id: data?.folder_id || '',
      };
      const response = await getApiData<
        {
          access_role: string;
          email: string;
          // folder_id: string;
        },
        sendInviteProps
      >({
        url: Endpoints.inviteMember,
        method: 'POST',
        data: sendData,
      });
      if (response && response.status === true) {
        notification.success({
          message: t('users.invitation_sent_title'),
          description: t('users.invitation_sent_description'),
        });
        getUsersList();
        setOpen(false);
        setLoading(false);
      } else {
        notification.error({
          message: t('users.invitation_failed_title'),
          description:
            response?.message || t('users.invitation_failed_description'),
        });
        setLoading(false);
      }
    } catch (error) {
      console.log('🚀 ~ handleSendInvite ~ error:', error);
      notification.error({
        message: t('users.invitation_failed_title'),
        description: t('users.invitation_failed_description'),
      });
      setLoading(false);
    }
  };
  return (
    <div className={styles.container}>
      <Row gutter={sm ? [10, 0] : [16, 0]}>
        <Col xs={24}>
          <Title level={4} className={styles.title}>
            {t('userManagement.title')}
          </Title>
        </Col>
        <Col xs={xxs ? 24 : 16} sm={17} md={17} lg={18} xl={20}>
          <Input
            className={styles.searchInput}
            placeholder={t('userManagement.searchPlaceholder')}
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={e => {
              setSearchValue(e.target.value);
              debouncedSearch(e.target.value);
            }}
            allowClear
            onClear={() => {
              setSearchValue('');
              getUsersList(1, '', true);
            }}
          />
        </Col>
        <Col
          xs={xxs ? 24 : 8}
          sm={7}
          md={7}
          lg={6}
          xl={4}
          style={{
            marginTop: xxs ? '10px' : '0px',
          }}
        >
          <Button
            type='primary'
            style={{
              width: '100%',
            }}
            className={styles.inviteBtn}
            onClick={() => {
              setOpen(true);
            }}
          >
            + {t('userManagement.invite')}
          </Button>
        </Col>
      </Row>
      <InviteMemberModal
        visible={open}
        onClose={() => {
          setOpen(false);
        }}
        onSendInvite={data => handleSendInvite(data)}
        btnLoader={loading}
        type='userManagement'
      />
      {/* <Table
        loading={loading}
        className={styles.table}
        columns={columns}
        dataSource={usersListData}
        pagination={false}
        onRow={record => ({
          onClick: () => {
            router.push(`/sharing/sharing-details?id=${record.user_id}`);
          },
        })}
      /> */}
      <ResponsiveTable
        loading={loading}
        columns={columns}
        dataSource={usersListData}
        pagination={false}
        onRow={record => ({
          onClick: () => {
            router.push(`/sharing/sharing-details?id=${record.user_id}`);
          },
        })}
      />
      {pagination?.totalPage && pagination?.totalPage > 1 ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
          }}
        >
          <Pagination
            current={pagination.currentPage}
            pageSize={pagination.pageSize}
            total={pagination.totalCount || 0}
            onChange={p => {
              setPagination(prev => ({
                ...prev,
                currentPage: p,
              }));
              getUsersList(p, searchValue, true);
            }}
            showSizeChanger={false}
          />
        </div>
      ) : null}

      <Modal
        open={openConfirmModal}
        onCancel={() => {
          setOpenConfirmModal(false);
          setRemoveId('');
        }}
        footer={null}
        centered
        width={500}
        closable={true}
      >
        <Row
          gutter={[16, 16]}
          style={{
            padding: sm ? '10px' : '20px',
          }}
        >
          <Col
            xs={24}
            style={{
              textAlign: 'center',
            }}
          >
            <Text className={styles.titleText}>
              {t('userManagement.removeUserText')}
            </Text>
            <div
              style={{
                marginTop: '15px',
              }}
            >
              <Text className={styles.text}>
                {t('userManagement.confirmRemoveAccessUser')}
              </Text>
            </div>
          </Col>

          <Col
            xs={24}
            style={{
              textAlign: 'center',
              marginTop: '10px',
            }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={12}>
                <Button
                  type='default'
                  onClick={() => {
                    setOpenConfirmModal(false);
                    setRemoveId('');
                  }}
                  style={{
                    width: '100%',
                  }}
                >
                  {t('userManagement.cancel')}
                </Button>
              </Col>
              <Col xs={12}>
                <Button
                  type='primary'
                  onClick={() => {
                    removeUser();
                  }}
                  style={{
                    width: '100%',
                  }}
                  loading={removeLoading}
                >
                  {t('userManagement.remove')}
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Modal>
    </div>
  );
};

export default UserManagement;
