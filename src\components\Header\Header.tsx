/**
 * @fileoverview Dashboard header component with user greeting and navigation
 * @module components/Header/Header
 */

'use client';

import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { BellOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Col, Layout, Select, Typography } from 'antd';
import { capitalize } from 'lodash-es';
import { usePathname, useRouter } from 'next/navigation';
import { JSX, useCallback } from 'react';
import ReactCountryFlag from 'react-country-flag';
import { Language, setLanguage } from '../../store/slices/languageSlice';
import ThemeToggle from '../ui/ThemeToggle';
import styles from './Header.module.css';

const { Header } = Layout;
const { Option } = Select;
const { Title, Text } = Typography;

/**
 * Dashboard header component that displays user greeting, subtitle, and navigation elements
 *
 * @component
 * @returns {JSX.Element} The rendered header component
 *
 * @example
 * // Basic usage in a dashboard layout
 * <HeaderCmp />
 *
 * @description
 * This component renders the main dashboard header containing:
 * - Personalized greeting with user's name
 * - Subtitle with activity overview
 * - Notification bell icon
 * - User avatar
 *
 * The component automatically retrieves user information from Redux store
 * and uses internationalization for text content.
 */
export default function HeaderCmp(): JSX.Element {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(s => s.auth);
  const { t } = useTranslation();
  const name = user?.fullname || '';
  const router = useRouter();
  const sm = useMediaQuery('(max-width: 426px)');

  const { currentLanguage } = useTranslation();

  const pathname = usePathname();

  const handleLanguageChange = useCallback(
    (language: Language) => {
      dispatch(setLanguage(language));
    },
    [dispatch]
  );

  return (
    <Header className={styles.dashboardHeader}>
      <div className={styles.headerLeft}>
        <Title level={4} className={styles.welcomeTitle}>
          {pathname === '/sharing'
            ? t('header.userManagement')
            : t('header.greeting').replace('{name}', capitalize(name))}
        </Title>
        <Text className={styles.welcomeSubtitle}>
          {pathname === '/sharing'
            ? t('header.subtitle')
            : t('dashboard.subtitleHeader')}
        </Text>
      </div>

      <div className={styles.headerRight}>
        <Col>
          <Select
            value={currentLanguage}
            onChange={handleLanguageChange}
            style={{
              boxShadow: 'none',
            }}
            // style={{ width: '100%', marginTop: 8 }}
          >
            <Option value='en'>
              <ReactCountryFlag countryCode='US' svg /> English
            </Option>
            <Option value='de'>
              <ReactCountryFlag countryCode='DE' svg /> Deutsch
            </Option>
          </Select>
        </Col>
        {!sm && (
          <>
            <Col style={{ marginTop: 8 }}>
              <ThemeToggle size='large' />
            </Col>
            <Button
              type='default'
              onClick={() => {
                router.push('/notification-list');
              }}
              className={styles.user}
              icon={<BellOutlined />}
            />
          </>
        )}
        <Avatar
          onClick={() => {
            router.push('/settings/profile');
          }}
          className={styles.user}
          icon={<UserOutlined />}
        />
      </div>
    </Header>
  );
}
