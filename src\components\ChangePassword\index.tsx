import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Form, Input, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Endpoints } from '../../config/endpoints';
import { getApiData } from '../../helpers/ApiHelper';
import { useTranslation } from '../../hooks/useTranslation';
import { authStyles } from '../auth/AuthWrapper';
import VerificationModal from '../modals/VerificationModal';
import { useNotification } from '../Notification/NotificationContext';
import styles from './changePassword.module.css';

const { Title } = Typography;

interface Response {
  status: boolean;
  message: string;
}

const ChangePassword = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const notification = useNotification();

  const [loading, setLoading] = useState(false);
  const [openVerificationModal, setOpenVerificationModal] = useState(false);

  // AntD form instance
  const [form] = Form.useForm();

  const onFinish = (values: {
    oldPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    ChangePasswordAPI(values);
  };

  const ChangePasswordAPI = async (values: {
    oldPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    try {
      setLoading(true);
      const response = await getApiData<
        {
          current_password: string;
          password: string;
          confirm_password: string;
        },
        Response
      >({
        url: `${Endpoints.updateCurrentPassword}`,
        method: 'POST',
        data: {
          password: values.newPassword,
          current_password: values.oldPassword,
          confirm_password: values.confirmPassword,
        },
      });

      if (response && response.status === true) {
        notification.success({
          message: t('myProfile.password_updated_success'),
          description: t('myProfile.password_updated_description'),
        });
        router.replace('/settings');
      } else {
        notification.error({
          message: t('myProfile.change_password_failed'),
          description:
            response?.message ||
            t('myProfile.profile_update_failed_description'),
        });
      }
    } catch (error) {
      console.log('🚀 ~ ChangePasswordAPI ~ error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Button
          type='link'
          icon={<ArrowLeftOutlined />}
          className={styles.backButton}
          onClick={() => {
            router.replace('/settings');
          }}
        >
          {t('common.back')}
        </Button>
      </div>

      <Title level={2} className={styles.title}>
        {t('changePassword.title')}
      </Title>

      <Form
        form={form}
        layout='vertical'
        className={styles.form}
        onFinish={() => {
          setOpenVerificationModal(true);
        }}
      >
        <Form.Item
          label={t('changePassword.old_password_label')}
          name='oldPassword'
          rules={[
            {
              required: true,
              message: t('changePassword.old_password_required'),
            },
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('changePassword.old_password_placeholder')}
            className={authStyles.input}
            onCopy={e => {
              e.preventDefault();
            }}
            onPaste={e => {
              e.preventDefault();
            }}
          />
        </Form.Item>

        <Form.Item
          label={t('changePassword.new_password_label')}
          name='newPassword'
          rules={[
            {
              required: true,
              message: t('changePassword.new_password_required'),
            },
            { min: 6, message: 'Password must be at least 6 characters long' },
            {
              max: 20,
              message: 'Password length must be less than 20 characters',
            },
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('changePassword.new_password_placeholder')}
            className={authStyles.input}
            onCopy={e => {
              e.preventDefault();
            }}
            onPaste={e => {
              e.preventDefault();
            }}
          />
        </Form.Item>

        <Form.Item
          label={t('changePassword.confirm_password_label')}
          name='confirmPassword'
          dependencies={['newPassword']}
          className={authStyles.formItem}
          rules={[
            {
              required: true,
              message: t('changePassword.confirm_password_required'),
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  new Error(t('changePassword.passwords_not_match'))
                );
              },
            }),
          ]}
        >
          <Input.Password
            placeholder={t('changePassword.confirm_password_placeholder')}
            className={authStyles.input}
            onCopy={e => {
              e.preventDefault();
            }}
            onPaste={e => {
              e.preventDefault();
            }}
          />
        </Form.Item>

        <Form.Item>
          <Button
            type='primary'
            className={authStyles.primaryButton}
            style={{ marginTop: 40 }}
            loading={loading}
            onClick={() => {
              // Validate form manually before opening modal
              form
                .validateFields()
                .then(() => {
                  setOpenVerificationModal(true);
                })
                .catch(() => {
                  // Do nothing if validation fails
                });
            }}
          >
            {t('changePassword.save_changes')}
          </Button>
        </Form.Item>
      </Form>

      <VerificationModal
        visible={openVerificationModal}
        onClose={() => {
          setOpenVerificationModal(false);
        }}
        onSuccess={async () => {
          const values = form.getFieldsValue();
          onFinish(values);
        }}
      />
    </div>
  );
};

export default ChangePassword;
