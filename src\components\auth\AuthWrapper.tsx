'use client';

import { Card } from 'antd';
import React from 'react';

import styles from './AuthWrapper.module.css';

interface AuthWrapperProps {
  children: React.ReactNode;
  showThemeToggle?: boolean;
  style?: React.CSSProperties;
}

export default function AuthWrapper({ children, style }: AuthWrapperProps) {
  return (
    <div className={styles.authContainer} style={style}>
      <Card className={styles.authCard}>{children}</Card>
    </div>
  );
}

// Export individual style classes for use in components
export const authStyles = styles;
