'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { logout } from '@/store/slices/authSlice';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CreditCardOutlined,
  FileTextOutlined,
  HeartOutlined,
  HistoryOutlined,
  LockOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Button, Card, Col, Modal, Row, Space, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import { ReactNode, useState } from 'react';
import { isMobile, isTablet } from 'react-device-detect';
import useMediaQuery from '../../hooks/useMediaQuery';
import { authStyles } from '../auth/AuthWrapper';
import styles from './settings.module.css';

const { Title, Text } = Typography;

export default function SettingsComp() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();
  const [logoutConfirmVisible, setLogoutConfirmVisible] = useState(false);
  const handleLogout = () => {
    dispatch(logout());
    router.replace('/');
  };
  const { user } = useAppSelector(state => state.auth);

  const isSocialLogin = user?.social_platform ? true : false;

  type SettingOptionProps = {
    icon: ReactNode;
    label: string;
    status?: ReactNode;
    showDivider?: boolean;
    onClick?: VoidFunction;
  };

  const SettingOption = ({
    icon,
    label,
    status,
    showDivider = true,
    onClick = () => {},
  }: SettingOptionProps) => {
    return (
      <Card
        className={
          showDivider ? styles.settingCard : styles.settingCardNoBorder
        }
        style={{
          cursor: isMobile || isTablet ? 'default' : 'pointer',
        }}
        onClick={() => {
          onClick();
        }}
      >
        <Row justify='space-between' align='middle'>
          <Col>
            <Space className='stat-description'>
              {icon}
              <Text className='stat-description'>{label}</Text>
            </Space>
          </Col>

          <Col>
            {status || <ArrowRightOutlined className='stat-description' />}
          </Col>
        </Row>
      </Card>
    );
  };
  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Button
          type='link'
          icon={<ArrowLeftOutlined />}
          className={styles.backButton}
          onClick={() => {
            router.replace('/dashboard');
          }}
        >
          {t('common.back')}
        </Button>
      </div>
      <Title className={authStyles.formTitle}>{t('common.settings')}</Title>

      <div className={styles.profileSection}>
        <Avatar
          size={120}
          src={user?.profile_pic}
          icon={!user?.profile_pic ? <UserOutlined /> : <></>}
        />
        <div className={styles.profileDetails}>
          <Title level={4} className='stat-label'>
            {user?.fullname}
          </Title>

          <Button
            className={styles.editButton}
            style={{
              backgroundColor: isDark ? '#8EDAFE' : '#001f3f',
              color: isDark ? 'black' : '#fff',
              width: 'fit-content',
            }}
            onClick={() => {
              router.replace('/settings/profile');
            }}
          >
            {t('common.edit_profile')}
          </Button>
        </div>
      </div>

      <Row gutter={16} style={{ marginTop: 50 }}>
        <Col xs={24} sm={24} md={12}>
          <SettingOption
            icon={<UserOutlined />}
            label={t('settings.account_status')}
            status={
              <>
                <Text type='success'>● </Text>
                <Text className='stat-description'>{t('settings.active')}</Text>
              </>
            }
          />
          <SettingOption
            icon={<HeartOutlined />}
            label={t('settings.saved_ideas')}
            onClick={() => router.replace('/my-ideas')}
          />
          <SettingOption
            icon={<FileTextOutlined />}
            label={t('settings.my_submissions')}
            onClick={() => router.replace('/my-ideas?type=sealed_documents')}
          />
          <SettingOption
            icon={<LockOutlined />}
            label={t('settings.2fa_authentication')}
            onClick={() => router.push('/settings/reset-2fa')}
          />
          {!isSocialLogin && (
            <SettingOption
              icon={<HistoryOutlined />}
              label={t('settings.transaction_history')}
              showDivider={sm || xs ? true : false}
              onClick={() => router.push('/settings/transaction-history')}
            />
          )}
        </Col>
        <Col xs={24} sm={24} md={12}>
          <SettingOption
            icon={<UserOutlined />}
            label={t('settings.account_type')}
            onClick={() => router.replace('/settings/account-type')}
          />
          <SettingOption
            icon={<CreditCardOutlined />}
            label={t('settings.subscription_plan')}
            onClick={() => router.replace('/settings/account-type')}
          />
          <SettingOption
            icon={<HistoryOutlined />}
            onClick={() => router.replace('/settings/account-history')}
            label={t('settings.activity_history')}
          />
          {!isSocialLogin && (
            <SettingOption
              icon={<LockOutlined />}
              label={t('settings.change_password')}
              onClick={() => router.replace('/settings/change-password')}
              showDivider={sm || xs ? false : true}
            />
          )}
          {isSocialLogin && (
            <SettingOption
              icon={<HistoryOutlined />}
              label={t('settings.transaction_history')}
              showDivider={sm || xs ? false : true}
              onClick={() => router.push('/settings/transaction-history')}
            />
          )}
        </Col>
      </Row>

      <div className={styles.actionButtons}>
        <Space direction='vertical' className={styles.fullWidth}>
          <Button
            className={styles.bottomButton}
            style={{
              backgroundColor: isDark ? '#8EDAFE' : '#001f3f',
              color: isDark ? 'black' : '#fff',
            }}
            onClick={() => router.back()}
          >
            {t('common.back')}
          </Button>
          <Button
            className={styles.bottomButton}
            style={{
              backgroundColor: isDark ? '#8EDAFE' : '#001f3f',
              color: isDark ? 'black' : '#fff',
            }}
            onClick={() => setLogoutConfirmVisible(true)}
          >
            {t('common.logout')}
          </Button>
          <Button type='primary' danger className={styles.upgradeButton}>
            {t('settings.upgrade_to_agency_account')}
          </Button>
        </Space>
        <Modal
          title={null}
          open={logoutConfirmVisible}
          onCancel={() => {
            setLogoutConfirmVisible(false);
          }}
          centered
          closable={false}
          onOk={() => {
            handleLogout();
            setLogoutConfirmVisible(false);
          }}
          width={400}
        >
          <Space direction='vertical' style={{ width: '100%' }} size='large'>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                textAlign: 'center',
              }}
            >
              <Title
                level={5}
                style={{
                  color:
                    theme === 'dark'
                      ? 'var(--color-text-tertiary)'
                      : 'var(--color-text-primary)',
                }}
                className={styles.logoutTitle}
              >
                {t('sidebar.logoutPrompt')}
              </Title>
            </div>
          </Space>
        </Modal>
      </div>
    </div>
  );
}
