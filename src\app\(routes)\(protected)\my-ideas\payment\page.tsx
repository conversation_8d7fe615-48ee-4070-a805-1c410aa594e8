'use client';

import { Endpoints } from '@/config/endpoints';
import { useTheme } from '@/contexts/ThemeContext';
import { getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef } from 'react';
import { useNotification } from '../../../../../components/Notification/NotificationContext';
import styles from './page.module.css';

interface ApiRes {
  status: boolean;
  paymnt_status: string;
}

export default function Payment() {
  const { t } = useTranslation();
  const router = useRouter();
  const { theme } = useTheme();
  const searchParams = useSearchParams();
  const paymentId = searchParams.get('payment_id');
  const sessionId = searchParams.get('session_id');

  const isCancel = searchParams.get('cancel');

  const attemptRef = useRef(0);
  const maxAttempts = 10;
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const startedRef = useRef(false); // ✅ prevent double start in Strict Mode

  const notification = useNotification();

  useEffect(() => {
    if (!paymentId) {
      router.back();
      return;
    }

    const checkStatus = async (): Promise<void> => {
      attemptRef.current += 1;

      try {
        const url = isCancel
          ? `${Endpoints.checkPaymentStatus}/${paymentId}?cancel=true`
          : `${Endpoints.checkPaymentStatus}/${paymentId}`;

        const res = await getApiData<{ payment_id: string }, ApiRes>({
          url,
          method: 'GET',
        });

        if (res?.status === true && res?.paymnt_status === 'canceled') {
          notification.error({
            message: 'Error',
            description: 'You have cancelled the payment process.',
          });
          setTimeout(() => {
            router.replace(`/my-ideas`);
          }, 1000);
          return;
        }

        if (res?.status === true && res?.paymnt_status === 'completed') {
          timeoutRef.current = setTimeout(() => {
            router.replace(`/my-ideas/payment/success?session_id=${sessionId}`);
          }, 1000);
          return; // stop polling
        }

        if (attemptRef.current < maxAttempts) {
          const nextDelay = 5000; // fixed 5 seconds
          timeoutRef.current = setTimeout(checkStatus, nextDelay);
        }
      } catch (err) {
        console.error('Error checking payment status:', err);
      }
    };

    if (!startedRef.current) {
      startedRef.current = true;
      checkStatus();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [paymentId, sessionId, router, isCancel]);

  return (
    <div
      className={`${styles.container} ${theme === 'dark' ? styles.dark : styles.light}`}
    >
      <div className={styles.card}>
        <div className={styles.loader} />
        <h1 className={styles.title}>{t('paymentStatus.processTitle')}</h1>
        <p className={styles.subtitle}>{t('paymentStatus.subtitle')}</p>
      </div>
    </div>
  );
}
