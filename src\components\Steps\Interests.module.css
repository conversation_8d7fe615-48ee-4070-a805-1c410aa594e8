.container {
  width: 100%;
  text-align: center;
}

.topBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress {
  display: flex;
  gap: 10px;
}

.step {
  height: 6px;
  width: 80px;
  border-radius: 10px;
}

.active {
  background: var(--color-primary);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  max-width: 600px;
  margin: 0 auto 40px;
}

.tag {
  background: #f5f5f5;
  border-radius: 24px;
  padding: 10px 20px;
  font-size: 18px;
  transition: all 0.3s;
  color: #555555;
  font-weight: 300;
}

:global(html[data-theme='dark']) .tag {
  background-color: #375278;
  color: #ffffff;
}

:global(html[data-theme='dark']) .selected {
  background-color: var(--color-secondary);
  color: var(--color-primary);
}

/* Hover effect only on desktop */
@media (hover: hover) and (pointer: fine) {
  .tag:hover {
    background: var(--color-secondary);
  }
}
.nextBtn {
  width: 100%;
}

.selected {
  background-color: var(--color-secondary);
  color: var(--color-primary);
}

@media (max-width: 1200px) {
  .tag {
    font-size: 16px;
  }
}

@media (max-width: 600px) {
  .tag {
    font-size: 14px;
  }
}
