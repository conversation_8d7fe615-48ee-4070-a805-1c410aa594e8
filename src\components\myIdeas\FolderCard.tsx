'use client';

import {
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { Card, Dropdown, MenuProps, Tag, Tooltip, Typography } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

import { useTranslation } from '@/hooks/useTranslation';
import { colors } from '@/theme/antdTheme';
import { getStatusText, StatusCode } from '@/utils/CommonFunctions';
import { UploadFile } from 'antd/es/upload/interface';
import dayjs from 'dayjs';
import { TbBulbFilled } from 'react-icons/tb';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useAppSelector } from '../../store/hooks';
import styles from './FolderCard.module.css';

const { Text } = Typography;

export interface CombinedFile extends UploadFile {
  _id?: string;
  file_name?: string;
  file_size?: string | number;
  fileName?: string;
  file_url?: string;
  fullname?: string;
  createdAt?: Date;
}

export interface FolderData {
  _id: string;
  name: string;
  description: string;
  color: string;
  status: StatusCode;
  phoneNumber?: string;
  createdAt?: Date;
  first_step?: boolean;
  files: CombinedFile[];
  notes?: string;
  total_file_storage?: number;
  folder_access_role?: string;
  shared_by_owner_id?: string;
  fullname?: string;
  sealed_timestamp: string;
}

interface FolderCardProps {
  folder: FolderData;
  onEdit: (folder: FolderData) => void;
  onDelete: (folder: FolderData) => void;
  onClick: (folder: FolderData) => void;
  onSave: (folder: FolderData) => void;
  style?: React.CSSProperties;
}

const FolderCard: React.FC<FolderCardProps> = ({
  folder,
  onEdit,
  onDelete,
  onClick,
  onSave,
  style,
}) => {
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [editableName, setEditableName] = useState(folder.name);
  const inputRef = useRef<HTMLInputElement>(null);
  const { user } = useAppSelector(s => s.auth);
  const handleCardClick = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest('.ant-dropdown-trigger')) {
      return;
    }
    onClick(folder);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(folder);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(folder);
  };

  const handleSave = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSave(folder);
  };

  const handleMenuClick: MenuProps['onClick'] = info => {
    if (info.key === 'edit') {
      handleEdit(info.domEvent as unknown as React.MouseEvent);
    } else if (info.key === 'delete') {
      handleDelete(info.domEvent as unknown as React.MouseEvent);
    } else if (info.key === 'save') {
      handleSave(info.domEvent as unknown as React.MouseEvent);
    }
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleBlur = () => {
    setIsEditing(false);
    if (editableName.trim() && editableName !== folder.name) {
      onEdit({ ...folder, name: editableName.trim() });
    } else {
      setEditableName(folder.name); // Reset if unchanged
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      (e.target as HTMLInputElement).blur();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditableName(folder.name);
    }
  };

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const menuItems: MenuProps['items'] = [
    {
      key: 'edit',
      label: t('myIdeas.actions.edit'),
      icon: <EditOutlined />,
    },
    {
      key: 'delete',
      label: t('myIdeas.actions.delete'),
      icon: <DeleteOutlined />,
      danger: true,
    },
    {
      key: 'save',
      label: t('myIdeas.actions.save'),
      icon: <SaveOutlined />,
    },
  ];

  const sm = useMediaQuery('(max-width: 600px)');

  return (
    <Card className={styles.styledCard} onClick={handleCardClick} style={style}>
      {folder?.shared_by_owner_id === user?._id && (
        <div className={styles.actionButton}>
          <Dropdown
            menu={{ items: menuItems, onClick: handleMenuClick }}
            trigger={['click']}
            placement='bottomRight'
          >
            <MoreOutlined />
          </Dropdown>
        </div>
      )}

      <div className={styles.cardContent}>
        <div>
          <div
            className={styles.folderIcon}
            style={{ backgroundColor: folder.color }}
          >
            <TbBulbFilled color={colors.white} size={sm ? 25 : 20} />
          </div>

          <Text className={styles.date}>
            {dayjs(folder.createdAt).format('DD-MM-YYYY')}
          </Text>

          {isEditing ? (
            <input
              ref={inputRef}
              className={styles.editInput}
              value={editableName}
              onChange={e => setEditableName(e.target.value)}
              onBlur={handleBlur}
              onKeyDown={handleKeyDown}
            />
          ) : (
            <Text
              className={styles.folderName}
              onDoubleClick={handleDoubleClick}
              title={t('myIdeas.actions.doubleClickToRename')}
            >
              {folder.name}
            </Text>
          )}

          <Tooltip title={folder.description} placement='bottom'>
            <Text className={styles.folderDescription}>
              {folder.description}
            </Text>
          </Tooltip>
        </div>

        <Tag
          className={`${styles.statusTag} ${styles[`statusTag${folder.status === '2' ? 'InReview' : getStatusText(folder.status)}`]}`}
        >
          {getStatusText(folder.status)}
        </Tag>
      </div>
    </Card>
  );
};

export default FolderCard;
