import { Col, Divider, <PERSON>, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useAppSelector } from '../../store/hooks';
import FileCard from '../FileCard/FileCard';
import styles from './AllFiles.module.css';
import { CombinedFile, FolderData } from './FolderCard';

const { Text } = Typography;

const AllFiles = ({ folderDetails }: { folderDetails?: FolderData | null }) => {
  const { user } = useAppSelector(s => s.auth);
  const [files, setFiles] = useState<CombinedFile[]>(
    folderDetails?.files || []
  );
  useEffect(() => {
    setFiles(folderDetails?.files || []);
  }, [folderDetails?.files]);

  return (
    <div className={styles.container}>
      <div className={styles.documentsContainer}>
        <Text className={styles.documents}>Documents</Text>
        <Divider className={styles.documentsDivider} />
      </div>
      <Row gutter={[12, 16]}>
        {folderDetails?.status === '3' ? (
          <>
            <Col xl={6} lg={8} md={12} sm={12} xs={12}>
              <FileCard
                type='Sealed'
                style={{
                  minWidth: '100%',
                }}
                size={folderDetails?.total_file_storage || 0}
                filename={
                  folderDetails?.files
                    ? folderDetails?.files?.length > 1
                      ? `${folderDetails?.files?.length} Sealed Files`
                      : `${folderDetails?.files?.length} Sealed File`
                    : 'Sealed File'
                }
                creator={
                  folderDetails?.files && folderDetails?.files?.length > 0
                    ? folderDetails?.files[0]?.fullname
                    : '-'
                }
                createdAt={
                  folderDetails?.files && folderDetails?.files?.length > 0
                    ? folderDetails?.files[0]?.createdAt
                    : '-'
                }
                creatorId={folderDetails?.shared_by_owner_id}
                acessRole={folderDetails?.folder_access_role}
              />
            </Col>
            <Col xl={6} lg={8} md={12} sm={12} xs={12}>
              <FileCard
                type='Proof'
                style={{
                  minWidth: '100%',
                  height: '100%',
                }}
                showSize={false}
                filename={'PDF with proof'}
                creator={
                  folderDetails?.files && folderDetails?.files?.length > 0
                    ? folderDetails?.files[0]?.fullname
                    : '-'
                }
                createdAt={folderDetails?.sealed_timestamp}
                creatorId={folderDetails?.shared_by_owner_id}
              />
            </Col>
          </>
        ) : (
          files?.map(file => (
            <Col key={file.uid} xl={6} lg={8} md={12} sm={12} xs={12}>
              <FileCard
                key={file.uid}
                filename={file.file_name}
                size={String(file.file_size)}
                style={{ minWidth: '100%' }}
                file_url={file.file_url}
                creator={files && files?.length > 0 ? files[0]?.fullname : '-'}
                createdAt={
                  files && files?.length > 0 ? files[0]?.createdAt : '-'
                }
                creatorId={folderDetails?.shared_by_owner_id}
                acessRole={folderDetails?.folder_access_role}
                fileId={file?._id}
                showDelete={
                  folderDetails?.status === '0' &&
                  folderDetails?.shared_by_owner_id === user?._id
                    ? true
                    : false
                }
                folderId={folderDetails?._id}
                removedFile={(fileId: string | number) => {
                  setFiles(prev => prev.filter(n => n._id !== fileId));
                }}
              />
            </Col>
          ))
        )}
      </Row>
    </div>
  );
};

export default AllFiles;
