'use client';

import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import { formatBytes, getChunkSize } from '@/utils/CommonFunctions';
import { uploadProcessStart } from '@/utils/UploadFile';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Col, Progress, Row, Typography, Upload } from 'antd';
import { RcFile } from 'antd/es/upload';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { usePathname, useRouter } from 'next/navigation';
import { JSX, useRef, useState } from 'react';
import { AiOutlineClose } from 'react-icons/ai';
import { BiSolidCloudUpload } from 'react-icons/bi';
import { useTheme } from '../../contexts/ThemeContext';
import useMediaQuery from '../../hooks/useMediaQuery';
import { FolderData } from '../myIdeas';
import { useNotification } from '../Notification/NotificationContext';
import styles from './page.module.css';

const { Title } = Typography;

export interface SASResponse {
  url: string;
  chunkSize?: number;
  status: boolean;
  message: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
}

export interface UploadProgress {
  percent: number;
  status: 'uploading' | 'done' | 'error';
}

export interface FileUploadParams {
  file_id: string;
  file_url: string;
  chunkIndex?: number;
  status: string;
  file_size?: number;
}

interface Props {
  setFolderDetails: (folderDetails: FolderData | null) => void;
  folderDetails: FolderData | null;
  updateFolderDetails: () => void;
}

/**
 * UploadArea component supports chunked, resumable, multi-file upload to Azure Blob Storage.
 * Utilizes backend to fetch SAS URLs for secure uploads.
 */
export default function UploadArea({
  updateFolderDetails,
}: Props): JSX.Element {
  const pathname = usePathname();
  const notification = useNotification();
  const { t } = useTranslation();
  const [loader, setLoader] = useState(false);
  const [cancelLoader, setCancelLoader] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [progressMap, setProgressMap] = useState<
    Record<string, UploadProgress>
  >({});
  const abortControllers = useRef<Record<string, AbortController>>({});
  const { user } = useAppSelector(state => state.auth);
  const folderId = pathname.split('/')?.pop();

  const theme = useTheme();

  const router = useRouter();

  // Add ref to track if total size notification has been shown
  const totalSizeNotificationShown = useRef(false);

  const MAX_FILE_SIZE =
    user?.is_basic_plan === true
      ? Number(user?.basic_plan_storage) - Number(user?.total_uploaded_storage)
      : Number(user?.subscriptionData?.priceData?.metadata?.total_storage) -
        Number(user?.total_uploaded_storage);

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    beforeUpload: file => {
      if (MAX_FILE_SIZE === 0) {
        notification.error({
          message: 'Storage Full',
          description: `Your storage is full. Please upgrade your plan to upload more files.`,
        });
        return Upload.LIST_IGNORE;
      }

      if (file.size > MAX_FILE_SIZE) {
        notification.error({
          message: 'File too large',
          description: `File should be less than ${formatBytes(MAX_FILE_SIZE)}`,
        });
        return Upload.LIST_IGNORE;
      }

      // Calculate total size of all files being uploaded
      const currentTotalSize = fileList.reduce(
        (total, f) => total + (f.size || 0),
        0
      );
      const newTotalSize = currentTotalSize + file.size;

      if (newTotalSize > MAX_FILE_SIZE) {
        notification.error({
          key: 'total-size-exceeded',
          message: 'Total size exceeded',
          description: `The total size of all files must be less than ${formatBytes(MAX_FILE_SIZE)}`,
        });
        return Upload.LIST_IGNORE;
      }

      return false;
    },
    onChange(info) {
      const validFiles = info.fileList.filter(
        file => file.size === undefined || file.size <= MAX_FILE_SIZE
      );
      const totalSize = validFiles.reduce(
        (total, file) => total + (file.size || 0),
        0
      );

      if (totalSize <= MAX_FILE_SIZE) {
        setFileList(validFiles);
        totalSizeNotificationShown.current = false;
      } else {
        if (!totalSizeNotificationShown.current) {
          notification.error({
            message: 'Total size exceeded',
            description: `The total size of all files must be less than ${formatBytes(MAX_FILE_SIZE)}`,
          });
          totalSizeNotificationShown.current = true;
        }
      }
    },
    fileList,
    showUploadList: false,
  };

  interface UpdateFolderRequest {
    _id?: string;
    first_step: boolean;
  }

  const updateFolderStep = async (type?: string) => {
    if (type === 'cancel') {
      setCancelLoader(true);
    }
    try {
      const res = await getApiData<UpdateFolderRequest, ApiResponse>({
        url: Endpoints.updateFolder,
        method: 'POST',
        data: {
          _id: folderId,
          first_step: false,
        },
      });

      if (res && res.status === true) {
        updateFolderDetails();
      } else {
        notification.error({
          message: 'Error',
          description: res?.message || 'Please try again later.',
        });
      }
      if (type === 'cancel') {
        setCancelLoader(false);
      }
    } catch (error) {
      if (type === 'cancel') {
        setCancelLoader(false);
      }
      notification.error({
        message: 'Error',
        description: error?.message || 'Please try again later.',
      });
    }
  };

  interface SasRequest {
    fileName: string;
    file_type: string;
    file_size: number;
    userId: string;
    folder_id: string;
    totalChunks: number;
  }

  /**
   * Uploads a single file in chunks to Azure using SAS URL.
   * Supports pause/resume and cancel via AbortController.
   */
  const uploadFile = async (
    file: UploadFile,
    isLast: boolean
  ): Promise<void> => {
    const originFile = file?.originFileObj || (file as RcFile);
    console.log('originFile ====>', originFile);
    if (!originFile) {
      return;
    }

    try {
      const controller = new AbortController();
      abortControllers.current[file.uid] = controller;

      const res = await getApiData<SasRequest, SASResponse>({
        url: Endpoints.getSasToken,
        method: 'POST',
        data: {
          userId: user?._id || '',
          fileName: originFile.name,
          file_size: originFile.size,
          file_type: originFile.type,
          folder_id: folderId || '',
          totalChunks: Math.ceil(
            originFile.size / getChunkSize(originFile.size)
          ), //getChunkSize(originFile.size),
        },
      });

      if (res && res.status === true) {
        console.log('SAS URL:', res.data.sasUrl);
        const data = res?.data || {};
        await uploadProcessStart(
          data,
          file,
          controller,
          file,
          updateFolderStep,
          setProgressMap,
          notification,
          isLast
        );
      } else {
        notification.error({
          message: 'Error',
          description: res?.message || 'Please try again later.',
        });
        throw new Error('SAS URL missing');
      }
    } catch (err) {
      if (err.name === 'AbortError') {
        notification.error({
          message: `${originFile.name} upload canceled.`,
        });
      } else {
        notification.error({
          message: `Error uploading ${originFile.name}`,
        });
      }
      setProgressMap(prev => ({
        ...prev,
        [file.uid]: { percent: 0, status: 'error' },
      }));
      setLoader(false);
    }
  };

  const handleRemoveFile = (uid: string) => {
    setFileList(prevList => prevList.filter(file => file.uid !== uid));
    // Reset notification flag when files are removed
    totalSizeNotificationShown.current = false;
  };

  /** Upload all files in queue */
  const handleUpload = async (): Promise<void> => {
    if (fileList?.length === 0) {
      notification.error({
        message: t('view.selectFile'),
      });
      return;
    }
    setLoader(true);
    for (let index = 0; index < fileList.length; index++) {
      const file = fileList[index];
      console.log(`Processing file at index ${index}:`, file);

      if (progressMap[file.uid]?.status === 'done') {
        continue;
      }

      const isLast = index === fileList.length - 1;

      await uploadFile(file, isLast);
    }
    return;
  };

  /** Cancel all ongoing uploads */
  const handleCancel = (): void => {
    updateFolderStep('cancel');
  };

  const sm = useMediaQuery('(max-width: 768px)');
  const xs = useMediaQuery('(max-width: 600px)');

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Row>
          <Col
            sm={24}
            style={{
              display: sm ? 'flex' : 'block',
              alignItems: 'flex-start',
              justifyContent: sm ? 'center' : 'flex-start',
              width: sm ? '100%' : '50%',
            }}
          >
            {sm && (
              <ArrowLeftOutlined
                style={{
                  fontSize: '20px',
                  color: '#000',
                  position: 'absolute',
                  left: xs ? '0px' : '20px',
                  marginTop: '5px',
                }}
                onClick={() => {
                  router.back();
                }}
              />
            )}

            <div
              style={{
                textAlign: sm ? 'center' : 'left',
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                flexDirection: sm ? 'column' : 'row',
              }}
            >
              <Title level={1} className={styles.pageTitle}>
                {t('view.title')}
              </Title>
            </div>
          </Col>
        </Row>
      </div>
      <div className={styles.wrapper}>
        <Upload.Dragger {...uploadProps} className={styles.dragger}>
          <BiSolidCloudUpload className={styles.icon} />
          <p className={styles.title}>{t('view.drop_files')}</p>
          <p className={styles.subTitle}>{t('view.supported_formats')}</p>
          <p className={styles.orText}>{t('view.or')}</p>
          <p className={styles.browse}>{t('view.browse')}</p>
        </Upload.Dragger>

        <div className={styles.progressGroup}>
          {fileList.map(file => (
            <div key={file.uid} style={{ marginTop: 12 }}>
              {/* Name + Cancel Icon */}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 8, // space between name and icon
                }}
              >
                <span>{file.name}</span>

                {/* Cancel icon */}
                {progressMap[file.uid]?.status === 'done' ||
                progressMap[file.uid]?.status === 'uploading' ? null : (
                  <span
                    onClick={() => handleRemoveFile(file.uid)}
                    style={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      backgroundColor: 'red',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                    }}
                  >
                    <AiOutlineClose size={12} />
                  </span>
                )}
              </div>
              {progressMap[file.uid]?.status === 'uploading' && (
                <Progress
                  strokeColor={theme?.theme === 'dark' ? '#8EDAFE' : '#012458'}
                  percent={progressMap[file.uid]?.percent || 10}
                  status={
                    progressMap[file.uid]?.status === 'error'
                      ? 'exception'
                      : progressMap[file.uid]?.status === 'done'
                        ? 'success'
                        : 'active'
                  }
                />
              )}
            </div>
          ))}
        </div>

        <div className={styles.btnParent}>
          <div className={styles.buttonGroup}>
            <Button
              type='primary'
              onClick={handleUpload}
              loading={loader}
              disabled={loader}
              style={{ width: '100%' }}
            >
              {t('view.upload')}
            </Button>
            <Button
              type='default'
              onClick={handleCancel}
              loading={cancelLoader}
              disabled={loader || cancelLoader}
              style={{ width: '100%' }}
            >
              {t('view.cancel')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
