'use client';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Spin, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FiEdit } from 'react-icons/fi';
import { Endpoints } from '../../config/endpoints';
import siteConfig from '../../config/site.config';
import { useTheme } from '../../contexts/ThemeContext';
import { getApiData } from '../../helpers/ApiHelper';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useTranslation } from '../../hooks/useTranslation';
import { useAppSelector } from '../../store/hooks';
import OTPVerificationModal from '../modals/OTPVerificationModal';
import TwoFactorAuthModal from '../modals/TwoFactorAuthModal';
import { useNotification } from '../Notification/NotificationContext';
import styles from './TwoFADetails.module.css';

const { Title, Text } = Typography;

interface user {
  _id: string;
  username: string;
  email: string;
  role: 'individual' | 'agency' | string;
  platform: 'web' | 'mobile' | string;
  isEmailVerified: boolean;
  mobile_number: string;
  country_code: string;
  status: '0' | '1' | string;
  notification_permission: boolean;
  createdAt: number;
  updatedAt: number;
}

interface VerifyOtpResponse {
  status: boolean;
  message: string;
  data?: user;
  token?: string;
  otp?: number;
}

const TwoFADetails = () => {
  const [visible, setVisible] = useState(false);
  const [open2FAModal, setOpen2FAModal] = useState(false);

  const { user } = useAppSelector(s => s.auth);

  const notification = useNotification();

  const [loading, setLoading] = useState(false);

  const generateOTP = async () => {
    setLoading(true);
    try {
      const response = await getApiData<
        { email: string; type: string },
        VerifyOtpResponse
      >({
        url: `${siteConfig.apiUrl}${Endpoints.resendOtp}`,
        method: 'POST',
        data: {
          email: user?.email || '',
          type: 'verify_2fa_otp',
        },
        customUrl: true,
      });
      if (response && response.status && 'otp' in response) {
        notification.success({
          message: 'OTP Sent',
          description: `Your OTP is ${response.otp}`,
        });
        setVisible(true);
        setLoading(false);
      } else {
        notification.error({
          message: 'Verification failed.',
          description:
            response?.message || 'Failed to resend code. Please try again.',
        });
        setLoading(false);
      }
    } catch {
      notification.error({
        message: 'Verification failed.',
        description: 'Failed to resend code. Please try again.',
      });
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  const sm = useMediaQuery('(max-width: 625px)');
  const { theme } = useTheme();

  const router = useRouter();

  const { t } = useTranslation();

  return (
    <div className={styles.container}>
      <div className={styles.contentContainer}>
        <div className={styles.header}>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.back();
            }}
          >
            {t('common.back')}
          </Button>
        </div>
        <Title level={2} className={styles.pageTitle}>
          {t('reset2FA.title')}
        </Title>
        <Text className={styles.subTitle}>{t('reset2FA.subtitle')}</Text>

        <div className={styles.preferredMethod}>
          <div
            style={{
              border:
                theme === 'dark' ? '1px solid #EDEFF1' : '1px solid #555555',
              padding: '10px',
              borderTopLeftRadius: '5px',
              borderTopRightRadius: '5px',
            }}
          >
            <Text className={styles.preferredMethodValue}>
              {t('reset2FA.twoFA')}
            </Text>
          </div>
          <div
            style={{
              borderBottom:
                theme === 'dark' ? '1px solid #EDEFF1' : '1px solid #555555',
              borderLeft:
                theme === 'dark' ? '1px solid #EDEFF1' : '1px solid #555555',
              borderRight:
                theme === 'dark' ? '1px solid #EDEFF1' : '1px solid #555555',
              padding: '10px',
              borderBottomLeftRadius: '5px',
              borderBottomRightRadius: '5px',
              display: 'flex',
              justifyContent: 'space-between',
              gap: '25px',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Text className={styles.preferredMethodTitle}>
                {t('reset2FA.auth_app')}
              </Text>
              <Text className={styles.preferredMethodDescription}>
                {t('reset2FA.subtitle1')}
              </Text>
            </div>
            {loading ? (
              <Spin
                size='small'
                style={{
                  marginTop: '10px',
                }}
              />
            ) : (
              <FiEdit
                style={{
                  color: theme === 'dark' ? '#fff' : '#000000',
                  fontSize: sm ? '30px' : '20px',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  generateOTP();
                }}
              />
            )}
          </div>
        </div>
        {/* <div className={styles.buttonContainer}>
          <Button
            onClick={() => {
              generateOTP();
              setVisible(true);
            }}
            className={styles.btn}
            type='primary'
            loading={loading}
          >
            Edit
          </Button>
        </div> */}
      </div>
      <OTPVerificationModal
        visible={visible}
        onClose={() => setVisible(false)}
        onSuccess={() => {
          setVisible(false);
          setOpen2FAModal(true);
        }}
      />
      <TwoFactorAuthModal
        visible={open2FAModal}
        onClose={() => setOpen2FAModal(false)}
        onSuccess={() => setOpen2FAModal(false)}
      />
    </div>
  );
};

export default TwoFADetails;
