.container {
  max-width: 800px;
  margin: 0 auto;
}
.header {
  padding: 20px 0px;
}

.backButton {
  padding: 0;
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
}

.backButton:hover {
  color: var(--color-text-primary);
}
.title {
  text-align: center;
  color: #fff !important;
  font-size: 46px;
  margin-bottom: 50px !important;
}

.profileSection {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
  text-align: left;
  margin-top: 28px;
}

.profileDetails {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.name {
  margin: 0;
  color: #000 !important;
  font-size: 30px;
}

.description {
  color: #555555;
  font-size: 18px;
}

.editButton {
  margin-top: 8px;
  width: fit-content;
  background-color: #edeff1;
  color: #555555;
  font-size: 18px;
}

.settingCard {
  border-radius: 0px;
  padding: 0px !important;
  border-left-width: 0px;
  border-right-width: 0px;
  border-top-width: 0px;
  border-bottom: 1px solid rgb(185, 183, 183) !important;
  background: transparent !important;
  cursor: pointer;
}

:global(html[data-theme='dark']) .settingCard {
  background-color: transparent !important;
}

.settingCardNoBorder {
  border-radius: 0px;
  padding: 0px !important;
  border: 0px;
  background: transparent !important;
  cursor: pointer;
}

:global(html[data-theme='dark']) .settingCardNoBorder {
  background-color: transparent !important;
}
.actionButtons {
  text-align: center;
  margin-top: 26px;
}

.fullWidth {
  width: 100%;
}

.bottomButton {
  color: #fff;
  padding: 0 32px;
  min-width: 280px;
  max-width: 90%;
  margin-bottom: 15px;
}
.bottomButton:hover {
  background: var(--color-primaryHover) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(1, 36, 88, 0.3) !important;
}
.editButton:hover {
  margin-top: 8px;
  width: fit-content;
  color: #555555;
  font-size: 18px;
  background: var(--color-primaryHover) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(1, 36, 88, 0.3) !important;
}
.upgradeButton:hover {
  background: #c9011999 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(1, 36, 88, 0.3) !important;
}

.upgradeButton {
  background-color: #d0021b !important;
  border-color: #d0021b !important;
  color: #fff !important;
  padding: 0 12px;
  min-width: 280px;
  max-width: 90%;
}

.optionLabel {
  color: #000;
}
.icon {
  color: #000;
}

.divider {
  background-color: black;
  margin: 0;
  /* margin-top: 20px; */
  /* margin-bottom: 20px; */
}
@media (max-width: 600px) {
  .actionButtons {
    margin-top: 15px;
  }
  .bottomButton {
    margin-bottom: 5px;
  }
}

@media (max-width: 480px) {
  .backButton {
    padding-left: 10px;
  }
}
