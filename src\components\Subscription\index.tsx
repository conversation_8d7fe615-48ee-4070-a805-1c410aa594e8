'use client';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import {
  bytesToMB,
  formatStorage,
  formatUnix,
  getUserData,
} from '@/utils/CommonFunctions';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Card, Col, Row, Typography } from 'antd';
import { capitalize } from 'lodash-es';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';
import { AccountModalProps, Plans, Prices } from '../AccountType';
import { useNotification } from '../Notification/NotificationContext';
import styles from './subscription.module.css';
const { Title, Text } = Typography;

interface Props {
  plan: Prices | null;
  upgrade?: boolean;
  duration?: boolean;
  planDetails?: Plans;
  modalData?: AccountModalProps;
  onCancel?: () => void;
}

const Subscription = ({
  plan,
  planDetails,
  upgrade,
  duration,
  modalData,
  onCancel = () => {},
}: Props) => {
  const router = useRouter();
  const { t } = useTranslation();
  const pathName = usePathname();
  const notification = useNotification();

  const isCurrentPlan = modalData?.isCurrentPlan || false;

  const { user } = useAppSelector(state => state.auth);

  const isActivePlanDuration =
    user?.subscriptionData?.priceData?.recurring?.interval;

  const planDuration = plan?.recurring?.interval;
  const isMonthlyPlan =
    isActivePlanDuration === 'month' && planDuration === 'month';
  // const isCurrentSwitchMonthly = duration && planDuration === 'month';

  const isBasicPlan = user?.is_basic_plan === true;

  const fromSubscription = pathName.includes('/subscription');
  const [loader, setLoader] = useState<string>('');

  const generateLink = async (type: string, duration?: string) => {
    setLoader(duration === 'yearly' ? 'yearly' : type);
    try {
      const res = await getApiData<
        { plan_id?: string | null; price_id?: string | null },
        ApiResponse
      >({
        url: Endpoints.getSubscriptionLink,
        method: 'POST',
        data: {
          price_id:
            duration === 'yearly'
              ? plan?.metadata?.yearly_plan_price_id
              : type === 'upgrade'
                ? plan && plan.id
                : plan?.metadata?.downgrade_price_id,
          plan_id: planDetails && planDetails._id,
        },
      });
      if (res && res.status === true && res.data) {
        if (res.data?.payment_url) {
          window.location.href = res.data.payment_url;
        }
      } else {
        notification.error({
          message: res?.message || 'Error generating link',
        });
      }
      setLoader('false');
    } catch (error) {
      setLoader('false');
      notification.error({
        message: error?.message || 'Error generating link',
      });
    }
  };

  const upgradeCall = async (type: string, duration?: string) => {
    setLoader(duration === 'yearly' ? 'yearly' : type);
    try {
      const res = await getApiData<
        {
          plan_id?: string | null;
          price_id?: string | null;
          subscription_id?: string | null;
        },
        ApiResponse
      >({
        url: Endpoints.upgradeSubscription,
        method: 'POST',
        data: {
          price_id:
            duration === 'yearly' && isMonthlyPlan
              ? plan?.metadata?.yearly_plan_price_id
              : plan && plan.id,
          plan_id: planDetails && planDetails._id,
          subscription_id: user?.subscriptionData?.stripeSubscriptionId,
        },
      });
      if (res && res.status === true) {
        notification.success({
          message: res?.message || `Subscription ${type} successfully`,
        });
        onCancel();
      } else {
        notification.error({
          message: res?.message || 'Error generating link',
        });
      }

      setTimeout(async () => {
        await getUserData();
        setLoader('');
      }, 2000);
    } catch (error) {
      setLoader('');
      notification.error({
        message: error?.message || 'Error generating link',
      });
    }
  };

  const cancelOrResumeSubscription = async (type: string) => {
    setLoader(type);
    try {
      const res = await getApiData<
        {
          subscription_id?: string | null;
          type: string;
        },
        ApiResponse
      >({
        url: Endpoints.resumeOrCancelSubscription,
        method: 'POST',
        data: {
          subscription_id: user?.subscriptionData?.stripeSubscriptionId,
          type,
        },
      });
      if (res && res.status === true) {
        notification.success({
          message: res?.message || `Subscription ${type} successfully`,
        });
        onCancel();
      } else {
        notification.error({
          message: res?.message || 'Error generating link',
        });
      }
      setTimeout(async () => {
        await getUserData();
        setLoader('');
      }, 2000);
    } catch (error) {
      setLoader('');
      notification.error({
        message: error?.message || 'Error generating link',
      });
    }
  };

  console.log(
    'plan',
    isCurrentPlan &&
      plan?.metadata?.downgrade_price_nickname &&
      plan?.metadata?.downgrade_price_id,
    plan?.metadata,
    plan?.metadata,
    isCurrentPlan,
    plan?.metadata?.cancle_subscription === '1'
  );

  const price =
    isBasicPlan && !upgrade
      ? 0
      : isCurrentPlan
        ? user?.subscriptionData?.priceData?.unit_amount
        : `${plan?.unit_amount}/${plan?.recurring?.interval}`;

  const title =
    isBasicPlan && !upgrade
      ? t('accountType.basic_free_tier') +
        ' ' +
        bytesToMB(user?.basic_plan_storage) +
        'Mb)'
      : isCurrentPlan
        ? user?.subscriptionData?.priceData?.nickname
        : plan?.nickname;

  const storageLimit =
    isBasicPlan && !upgrade
      ? bytesToMB(user?.basic_plan_storage)
      : isCurrentPlan
        ? user?.subscriptionData?.priceData?.metadata?.MB
        : plan?.metadata?.MB;

  const isDowngradeBasicAvailable =
    plan?.unit_amount === 0 &&
    user?.subscriptionData &&
    plan?.id &&
    !user?.subscriptionData?.cancelAtPeriodEnd;

  const isLowPricePlan =
    plan && user?.subscriptionData?.priceData
      ? plan?.metadata?.MB < user?.subscriptionData?.priceData?.metadata?.MB
      : false;

  const isGreaterPricePlan =
    plan && user?.subscriptionData?.priceData
      ? plan?.metadata?.MB > user?.subscriptionData?.priceData?.metadata?.MB
      : false;

  const isCurrentPlanSizeSameOnYearly =
    plan &&
    user?.subscriptionData?.priceData &&
    plan?.recurring?.interval === 'year' &&
    user?.subscriptionData?.priceData?.recurring?.interval === 'month'
      ? plan?.metadata?.MB === user?.subscriptionData?.priceData?.metadata?.MB
      : false;

  const btnLabel = isLowPricePlan
    ? 'subscription.downgrade'
    : isGreaterPricePlan
      ? 'subscription.upgradeTo'
      : isActivePlanDuration === 'year'
        ? 'subscription.subscribeMonthly'
        : isBasicPlan
          ? 'subscription.subscribeMonth'
          : isCurrentPlanSizeSameOnYearly
            ? 'subscription.upgradeToYearly'
            : 'subscription.subscribeMonth';

  return (
    <div className={styles.container}>
      {!plan && !fromSubscription && (
        <div className={styles.header}>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.replace('/settings/subscription-plan');
            }}
          >
            {t('common.back')}
          </Button>
        </div>
      )}
      <Title level={2} className={styles.heading}>
        {t(
          upgrade && !isCurrentPlan
            ? 'subscription.upgradeTo'
            : 'subscription.title'
        )}
        {'  '}
        {upgrade && plan?.nickname}
      </Title>

      <Card className={styles.card}>
        <div className={styles.iconWrapper}>
          <Image src={images.crown} alt='Plan Icon' width={60} height={60} />
        </div>

        <div className={styles.infoGrid}>
          <Row className={styles.infoRow}>
            <Col span={12}>
              <Text className={styles.label}>
                {t(
                  upgrade
                    ? 'subscription.title'
                    : 'subscription.active_subscription'
                )}
              </Text>
            </Col>
            <Col span={12} className={styles.value}>
              {title}
            </Col>
          </Row>
          <Row className={styles.infoRow}>
            <Col span={12}>
              <Text className={styles.label}>
                {t('subscription.storage_limit')}
              </Text>
            </Col>
            <Col span={12} className={styles.value}>
              {formatStorage(Number(storageLimit), 'MB')}
            </Col>
          </Row>
          <Row className={styles.infoRow}>
            <Col span={12}>
              <Text className={styles.label}>{t('subscription.price')}</Text>
            </Col>
            <Col span={12} className={styles.value}>
              ${price}
            </Col>
          </Row>
          {isCurrentPlan && !isBasicPlan ? (
            <>
              <Row className={styles.infoRow}>
                <Col span={12}>
                  <Text className={styles.label}>
                    {t('subscription.billed_on')}
                  </Text>
                </Col>
                <Col span={12} className={styles.value}>
                  {formatUnix(user?.subscriptionData?.currentPeriodStart)}
                </Col>
              </Row>
              <Row className={styles.infoRow}>
                <Col span={12}>
                  <Text className={styles.label}>
                    {t('subscription.status')}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text className={styles.active}>
                    {capitalize(user?.subscriptionData?.status)}
                  </Text>
                </Col>
              </Row>
              <Row>
                <Col span={12}>
                  <Text className={styles.label}>
                    {t('subscription.expired_on')}
                  </Text>
                </Col>
                <Col span={12} className={styles.value}>
                  {formatUnix(user?.subscriptionData?.currentPeriodEnd)}
                </Col>
              </Row>
            </>
          ) : null}
        </div>
      </Card>
      <div className={styles.buttonSection}>
        {plan?.unit_amount !== 0 && !fromSubscription && (
          <>
            <>
              {!isCurrentPlan && (
                <Button
                  type={isLowPricePlan ? 'default' : 'primary'}
                  className={styles.bottomButton}
                  loading={loader === 'upgrade' || loader === 'downgrade'}
                  onClick={() => {
                    if (isBasicPlan) {
                      generateLink('upgrade');
                    } else {
                      upgradeCall(isLowPricePlan ? 'downgrade' : 'upgrade');
                    }
                  }}
                >
                  {t(btnLabel)}
                  {'  '}
                  {isLowPricePlan || isGreaterPricePlan
                    ? plan?.nickname || 'Basic'
                    : null}
                </Button>
              )}

              {!isCurrentPlanSizeSameOnYearly &&
                (isActivePlanDuration !== 'year' ||
                  planDuration !== 'year') && (
                  <Button
                    type='primary'
                    className={styles.bottomButton}
                    loading={loader === 'yearly'}
                    onClick={() => {
                      if (isBasicPlan) {
                        generateLink('upgrade', 'yearly');
                      } else {
                        upgradeCall('upgrade', 'yearly');
                      }
                    }}
                  >
                    {t('subscription.subscribeYearly')}
                  </Button>
                )}
            </>
          </>
        )}
        {isDowngradeBasicAvailable && (
          <Button
            type='default'
            className={styles.bottomButton}
            loading={loader === 'downgrade' || loader === 'cancel'}
            onClick={() => {
              cancelOrResumeSubscription('cancel');
            }}
          >
            {t('subscription.downgrade')} {'  '}
            {plan?.metadata?.downgrade_price_nickname || 'Basic'}
          </Button>
        )}
        {/* <Button
          type='default'
          className={styles.bottomButton}
          onClick={() => {
            if (upgrade) {
              onCancel();
              return false;
            }
            router.back();
          }}
        >
          {t('common.back')}
        </Button> */}
      </div>
    </div>
  );
};

export default Subscription;
