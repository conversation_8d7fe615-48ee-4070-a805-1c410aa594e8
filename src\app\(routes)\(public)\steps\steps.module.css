.root {
  width: 700px;
  margin: 0 auto;
}

.formTitle {
  text-align: center !important;
  margin-bottom: 10px !important;
  font-weight: 600 !important;
  font-size: 46px !important;
  font-family: var(--font-radio-canada);
  margin-top: 10px;
}

.formSubtitle {
  display: block;
  text-align: center;
  margin-bottom: 10px;
  font-size: 18px;
  line-height: 1.4;
  font-family: var(--font-poppins);
  font-weight: 300;
  margin-top: 0px;
}

@media (max-width: 1200px) {
  .formTitle {
    font-size: 40px !important;
  }
  .root {
    width: 600px;
  }
  .formSubtitle {
    font-size: 16px !important;
  }
}

@media (max-width: 900px) {
  .formTitle {
    font-size: 32px !important;
  }
  .root {
    width: 550px;
  }
  .formSubtitle {
    font-size: 14px !important;
  }
}

@media (max-width: 600px) {
  .root {
    padding: 20px;
  }
  .formTitle {
    font-size: 24px !important;
  }
  .root {
    width: 85vw;
  }
  .formSubtitle {
    font-size: 12px !important;
  }
}

@media (max-width: 480px) {
  .root {
    width: 100vw;
  }
}
