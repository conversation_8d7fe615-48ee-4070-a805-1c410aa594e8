import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Recreate __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to version.json (go one level up from scripts/)
const filePath = path.join(__dirname, '..', '..', 'version.json');

// Read existing version.json or initialize
let versionData = { version: '0.0.0', buildDate: '' };

if (fs.existsSync(filePath)) {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  versionData = JSON.parse(fileContent);
}

let [major, minor, patch] = versionData.version.split('.').map(Number);

// 🔹 Version bump logic
if (patch < 9) {
  patch += 1; // Normal patch bump
} else {
  patch = 0;
  if (minor < 9) {
    minor += 1; // Reset patch, bump minor
  } else {
    minor = 0;
    major += 1; // Reset minor + patch, bump major
  }
}

const newVersion = `${major}.${minor}.${patch}`;
const buildDate = new Date().toISOString();

const newData = { version: newVersion, buildDate };

// Save updated version.json
fs.writeFileSync(filePath, JSON.stringify(newData, null, 2), 'utf8');

console.log(`✅ Updated version: ${newVersion} | Build Date: ${buildDate}`);
