import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { Button, Typography } from 'antd';
import dayjs from 'dayjs';
import Lot<PERSON> from 'lottie-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { isMobile } from 'react-device-detect';
import failedAnimationData from '../../../../public/lottie/failed.json';
import animationData from '../../../../public/lottie/sealSucess.json';
import { useNotification } from '../../Notification/NotificationContext';
import PaymentReceipt from '../PaymentReceipt/PaymentReceipt';
import styles from './PaymentStatus.module.css';

const { Title, Text } = Typography;

export interface PaymentStateProps {
  total: number;
  subtotal: number;
  price: number;
  card: string;
  cardBrand: string;
  cardLast4: string;
  receiptUrl: string;
  showReceipt: boolean;
  plan: string;
  planExpiredOn: string;
  invoiceId: string;
  transaction_id: string;
  address: string;
  amount: number | string;
  currency: string;
  email: string;
  extraCharges: number;
  issueDate: string;
  paymentDate: string;
  paymentSource: string;
  planName: string;
  userName: string;
  next_billing_on: string;
}

const BlankData: PaymentStateProps = {
  total: 0,
  subtotal: 0,
  price: 0,
  card: '',
  cardBrand: '',
  receiptUrl: '',
  cardLast4: '',
  showReceipt: false,
  plan: '',
  planExpiredOn: '',
  invoiceId: '',
  transaction_id: '',
  address: '',
  amount: 0,
  currency: '',
  email: '',
  extraCharges: 0,
  issueDate: '',
  paymentDate: '',
  paymentSource: '',
  planName: '',
  userName: '',
  next_billing_on: '',
};

const PaymentStatus = ({
  type = 'success',
}: {
  type: 'success' | 'failed';
}) => {
  const router = useRouter();
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const notification = useNotification();
  const sessionId = searchParams.get('session_id');
  const [state, setState] = useState<PaymentStateProps>(BlankData);

  const sm = useMediaQuery('(max-width: 768px)');
  const xs = useMediaQuery('(max-width: 600px)');

  const getDetails = async () => {
    try {
      const res = await getApiData<{ session_id: string }, ApiResponse>({
        url: `${Endpoints.getPaymentDetails}?session_id=${sessionId}`,
        method: 'GET',
      }); // your GET API
      if (res?.status === true && res?.data) {
        setState({ ...res.data });
      } else {
        notification.error({
          message: res?.message || 'Error fetching payment details',
        });
      }
    } catch (err) {
      notification.error({
        message: err?.message || 'Error fetching payment details',
      });
      console.error('Error checking payment status:', err);
    }
  };

  useEffect(() => {
    getDetails();
    return () => {};
  }, []);

  return (
    <>
      {state?.showReceipt ? (
        <PaymentReceipt type={type} data={state} />
      ) : (
        <div className={styles.container}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              width: '100%',
              justifyContent: sm ? 'center' : 'flex-start',
            }}
          >
            <Title level={1} className={styles.pageTitle}>
              {t('paymentStatus.title')}
            </Title>
          </div>

          <div className={styles.content}>
            <div className={styles.iconSection}>
              <Lottie
                animationData={
                  type === 'success' ? animationData : failedAnimationData
                }
                loop={true}
                style={{
                  width: isMobile ? '100px' : '130px',
                  height: isMobile ? '100px' : '130px',
                }}
              />
              <Title
                level={2}
                className={
                  type === 'success' ? styles.successText : styles.failedText
                }
              >
                {t('paymentStatus.' + type + 'Text')}
              </Title>
            </div>
            <div className={styles.textSection}>
              <div className={styles.propSection}>
                <Text className={styles.propTitle}>
                  {t('paymentStatus.subTotal')}
                </Text>
                <Text className={styles.propText}>${state?.subtotal}</Text>
              </div>
              {state?.price && (
                <div className={styles.propSection}>
                  <Text className={styles.propTitle}>
                    {t('paymentStatus.price')}
                  </Text>
                  <Text className={styles.propText}>${state?.price}</Text>
                </div>
              )}
              <div className={styles.propSection}>
                <Text className={styles.propTitle}>
                  {t('paymentStatus.billedOn')}
                </Text>
                <Text className={styles.propText}>
                  {dayjs(state?.issueDate).format('MMM DD, YYYY hh:mm A')}
                </Text>
              </div>
              <div className={styles.propSection}>
                <Text className={styles.propTitle}>
                  {t('paymentStatus.card')}
                </Text>
                <Text className={styles.propText}>
                  ********{state?.cardLast4}
                </Text>
              </div>
              <div className={styles.propSection}>
                <Text className={styles.propTitle}>
                  {t('paymentStatus.total')}
                </Text>
                <Text className={styles.propText}>${state?.total}</Text>
              </div>
            </div>
          </div>
          <div className={styles.buttonSection}>
            <Button
              type='primary'
              style={{ width: xs ? '100%' : '200px' }}
              onClick={() => {
                if (type === 'failed') {
                  router.back();
                  return;
                }
                setState(p => ({ ...p, showReceipt: true }));
              }}
            >
              {type === 'success' ? 'Receipt' : 'Try again'}
            </Button>
          </div>
        </div>
      )}
    </>
  );
};

export default PaymentStatus;
