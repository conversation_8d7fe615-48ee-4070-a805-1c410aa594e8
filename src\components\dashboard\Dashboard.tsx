'use client';

import { BulbOutlined, FolderOutlined, LockOutlined } from '@ant-design/icons';
import { Card, Col, Layout, Row, Typography } from 'antd';
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Title as ChartTitle,
  Legend,
  LinearScale,
  Tooltip,
} from 'chart.js';
import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';

import { useTranslation } from '@/hooks/useTranslation';
import '@/styles/dashboard-overrides.css';
import { colors } from '@/theme/antdTheme';
import { useRouter } from 'next/navigation';
import { RiDraftLine, RiFolderSharedLine } from 'react-icons/ri';
import { Endpoints } from '../../config/endpoints';
import { useTheme } from '../../contexts/ThemeContext';
import { ApiResponse, getApiData } from '../../helpers/ApiHelper';
import { APP_VERSION, BUILD_DATE } from '../../lib/version';
import { getUserData } from '../../utils/CommonFunctions';
import styles from './Dashboard.module.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ChartTitle,
  Tooltip,
  Legend
);

interface document {
  period: number;
  label: string;
  uploaded: number;
  sealed: number;
}

interface folder {
  period: number;
  label: string;
  newFolders: number;
  uploads: number;
  seals: number;
}

interface dashboardData {
  documentUploads: document[];
  folderActivity: folder[];
  allIdeasCount: number;
  sealedCount: number;
  draftCount: number;
  totalSharedCount: number;
}

const { Content } = Layout;
const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const theme = useTheme();

  const [dashboardData, setDashboardData] = useState<dashboardData>();

  // Chart data for Document Uploads Over Time
  const documentUploadsData = {
    labels: dashboardData?.documentUploads?.map(item => item.label) || [],
    datasets: [
      {
        label: t('dashboard.uploaded'),
        data: dashboardData?.documentUploads?.map(item => item.uploaded) || [],
        backgroundColor: colors.primary,
        borderRadius: 5,
        barThickness: 10,
      },
      {
        label: t('dashboard.sealed'),
        data: dashboardData?.documentUploads?.map(item => item.sealed) || [],
        backgroundColor: colors.green,
        borderRadius: 5,
        barThickness: 10,
      },
    ],
  };

  // Chart data for Folder & Idea Activity
  const folderActivityData = {
    labels: dashboardData?.folderActivity?.map(item => item.label) || [],
    datasets: [
      {
        label: t('dashboard.newFolder'),
        data: dashboardData?.folderActivity?.map(item => item.newFolders) || [],
        backgroundColor: colors.primary,
        borderRadius: 5,
        barThickness: 10,
      },
      {
        label: t('dashboard.uploads'),
        data: dashboardData?.folderActivity?.map(item => item.uploads) || [],
        backgroundColor: colors.secondary,
        borderRadius: 5,
        barThickness: 10,
      },
      {
        label: t('dashboard.seals'),
        data: dashboardData?.folderActivity?.map(item => item.seals) || [],
        backgroundColor: colors.green,
        borderRadius: 5,
        barThickness: 10,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
          color: theme?.theme === 'dark' ? 'white' : '#555555',
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: theme?.theme === 'dark' ? '#ffff' : '#555555',
          font: {
            size: 14,
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          color: theme?.theme === 'dark' ? '#ffff' : '#555555',
          stepSize: 20,
        },
      },
    },
  };

  const cardConfig = [
    {
      key: 'activeFolders',
      icon: <FolderOutlined style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      title: t('dashboard.goToMyIdeas'),
      subtitle: `${dashboardData?.allIdeasCount && dashboardData?.allIdeasCount > 0 ? dashboardData?.allIdeasCount : ''}  ${t('dashboard.foldersWithOngoingActivity')}`,
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas',
    },
    {
      key: 'ideaTemplates',
      icon: <RiDraftLine style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      bgColor: 'var(--color-white)',
      title: t('dashboard.draftFolders'),
      subtitle: `${dashboardData?.draftCount && dashboardData?.draftCount > 0 ? dashboardData?.draftCount : ''} ${t('dashboard.templatesForYourIdea')}`,
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas?type=draft',
    },

    {
      key: 'sealedDocuments',
      icon: <LockOutlined style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      bgColor: 'var(--color-white)',
      title: t('dashboard.sealedDocuments'),
      subtitle: `${dashboardData?.sealedCount && dashboardData?.sealedCount > 0 ? dashboardData?.sealedCount : ''} ${t('dashboard.sealedAndTimestampedFiles')}`,
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas?type=sealed_documents',
    },

    {
      key: 'sharedDocuments',
      icon: <RiFolderSharedLine style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      title: t('dashboard.sharedDocuments'),
      subtitle: `${dashboardData?.totalSharedCount && dashboardData?.totalSharedCount > 0 ? dashboardData?.totalSharedCount : ''} ${t('dashboard.share_sub')}`,
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas?type=shared_folder',
    },
  ];

  useEffect(() => {
    getUserData();
    getDashboardData();
  }, []);

  const getDashboardData = async () => {
    try {
      const res = await getApiData<
        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        {},
        ApiResponse & { data?: { total_storage?: number } }
      >({
        url: Endpoints.dashboardData,
        method: 'GET',
      });
      if (res && res.status === true) {
        setDashboardData(res?.data);
      }
    } catch (error) {
      console.log('Error fetching user data:', error);
    }
  };

  return (
    <div>
      <Content className={`dashboard-content ${styles.dashboardContent}`}>
        {/* Overview Section */}
        <div className={`overview-section ${styles.overviewSection}`}>
          <Title level={2} className={styles.sectionTitle}>
            {t('dashboard.overview')}
          </Title>

          <Row gutter={[16, 16]}>
            {cardConfig.map(card => (
              <Col key={card.key} xs={24} sm={12} md={12} lg={12} xl={6}>
                <Card
                  className={`overview-card ${styles.overviewCard}`}
                  style={{
                    background: card.bgColor,
                    borderRadius: '12px',
                    border:
                      card.bgColor === colors.secondary
                        ? 'none'
                        : '1px solid var(--color-border)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    height: '100%',
                  }}
                  onClick={() => {
                    router.push(card.screen);
                  }}
                  styles={{ body: { padding: '20px' } }}
                  hoverable
                >
                  <div style={{ textAlign: 'left' }}>
                    <div
                      style={{
                        width: 48,
                        height: 48,
                        background: card.iconBg,
                        borderRadius: 8,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: 'auto 0px 16px 0px',
                      }}
                    >
                      {card.icon}
                    </div>
                    <Title level={4} className={styles.cardTitle}>
                      {card.title}
                    </Title>
                    <Text className={styles.cardDescription}>
                      {card.subtitle}
                    </Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {/* My Ideas Section */}
        <div className={styles.myIdeasSection} style={{ marginTop: '32px' }}>
          <Title level={2} className={styles.sectionTitle}>
            {t('dashboard.myIdeas')}
          </Title>

          <Row gutter={[16, 16]}>
            {/* Safe New Idea Card */}
            <Col xs={24} sm={12} md={12} lg={12} xl={6}>
              <Card
                className={`idea-card ${styles.ideaCard}`}
                style={{
                  background: '#E8E8E8',
                  borderRadius: '12px',
                  border: '1px solid var(--color-border)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  height: '100%',
                }}
                onClick={() => {
                  router.push('/my-ideas/create-folder');
                }}
                styles={{ body: { padding: '20px' } }}
                hoverable
              >
                <div
                  style={{
                    textAlign: 'center',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                  }}
                >
                  <BulbOutlined
                    style={{
                      fontSize: '32px',
                      color: '#FFD700',
                      marginBottom: '16px',
                    }}
                  />
                  <Title
                    level={4}
                    style={{
                      fontSize: 'var(--font-base)',
                      fontWeight: 600,
                      marginBottom: '8px',
                      color: 'var(--color-text-primary)',
                    }}
                  >
                    {t('dashboard.safeNewIdea')}
                  </Title>
                  <Text
                    style={{
                      fontSize: 'var(--font-sm)',
                      color: 'var(--color-text-secondary)',
                    }}
                  >
                    {t('dashboard.safeYourNewIdeaOrCreateFolder')}
                  </Text>
                </div>
              </Card>
            </Col>

            {/* Idea Cards */}
            {[1, 2, 3].map(index => (
              <Col xs={24} sm={12} md={12} lg={12} xl={6} key={index}>
                <Card
                  className={`idea-card ${styles.ideaCard}`}
                  style={{
                    background: 'var(--color-white)',
                    borderRadius: '12px',
                    border: '1px solid var(--color-border)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    height: '100%',
                  }}
                  onClick={() => {
                    if (index === 1) {
                      router.push(`/my-ideas?type=draft`);
                      return;
                    } else if (index === 2) {
                      router.push(`/my-ideas?type=sealed_documents`);
                      return;
                    } else if (index === 3) {
                      router.push(`/my-ideas?type=in_review`);
                      return;
                    }
                    router.push('/my-ideas');
                  }}
                  styles={{ body: { padding: '20px', height: '100%' } }}
                  hoverable
                >
                  <div
                    style={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: '16px',
                        }}
                      >
                        <BulbOutlined
                          style={{
                            fontSize: '24px',
                            color: '#FFD700',
                          }}
                        />
                      </div>
                      <Title level={4} className={styles.ideaTitle}>
                        {index === 1
                          ? t('dashboard.draft_title')
                          : index === 2
                            ? t('dashboard.sealed_title')
                            : t('dashboard.in_review_title')}
                      </Title>
                      <Text className={styles.ideaDescription}>
                        {index === 1
                          ? t('dashboard.draft_sub')
                          : index === 2
                            ? t('dashboard.sealed_sub')
                            : t('dashboard.in_review_sub')}
                      </Text>
                    </div>
                    <div
                      style={{
                        marginTop: '16px',
                        backgroundColor:
                          index === 2 ? '#02CD271F' : '#8EDAFE2E',
                        padding: '5px',
                        borderRadius: '8px',
                        textAlign: 'center',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Text
                        style={{
                          fontSize: '14px',
                          color: index === 2 ? '#52c41a' : '#1890ff',
                          fontWeight: 400,
                        }}
                      >
                        {index === 2
                          ? t('dashboard.sealedOnBlockchain')
                          : index === 1
                            ? t('dashboard.inDrafts')
                            : t('dashboard.in_review')}
                      </Text>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {/* Your Document Activity Section */}
        <div
          className={styles.documentActivitySection}
          style={{ marginTop: '32px' }}
        >
          <Title level={2} className={styles.sectionTitle}>
            {t('dashboard.yourDocumentActivity')}
          </Title>

          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card title={t('dashboard.documentUploadsOverTime')}>
                <div style={{ height: '300px' }}>
                  <Bar data={documentUploadsData} options={chartOptions} />
                </div>
              </Card>
            </Col>

            {/* Folder & Idea Activity */}
            <Col xs={24} lg={12}>
              <Card title={t('dashboard.folderAndIdeaActivity')}>
                <div style={{ height: '300px' }}>
                  <Bar data={folderActivityData} options={chartOptions} />
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      </Content>
      <footer
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '20px',
        }}
      >
        <Text className={styles.versionDate}>
          Version: {APP_VERSION} | Released on:{' '}
          {new Date(BUILD_DATE).toLocaleString()}
        </Text>
      </footer>
    </div>
  );
};

export default Dashboard;
