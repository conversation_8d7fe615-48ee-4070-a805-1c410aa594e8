{"common": {"welcome": "Welcome", "logout": "Log Out", "dashboard": "Dashboard", "settings": "Settings", "appearance": "Appearance Settings", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "save": "Save", "cancel": "Cancel", "back": "Back", "edit_profile": "Edit Profile", "error": "Something went wrong! Try again later.", "loading": "Loading...", "upgrade": "Upgrade", "resume": "Resume", "back_to_user": "Back To User", "revoke": "Revoke", "invoice": "Invoice", "transaction_id": "Transaction ID", "resume_subscription": "Resume Subscription", "resume_info": "By resuming, your subscription will continue without cancellation at the end of the billing cycle.", "confirm": "Confirm", "proof_file": "PDF with proof"}, "navigation": {"dashboard": "Dashboard", "userManagement": "User Management", "billing": "Billing", "analytics": "Analytics", "appearanceSettings": "Appearance Settings"}, "dashboard": {"welcomeAgency": "Welcome Agency 👋", "subtitle": "Manage Your Teams, Client Projects, And Document Activity All In One Place.", "overview": "Overview", "activeClientFolders": "Active Client Folders", "documentsUploaded": "Documents Uploaded", "sealedDocuments": "Sealed Documents", "teamMembers": "Team Members", "clientFolders": "client folders", "filesUploaded": "files uploaded", "documentsSealed": "documents sealed", "teamMembersAcrossRoles": "team members across 3 roles", "clientFolder": "Client <PERSON>er", "folderName": "Folder Name", "createdDate": "Created Date", "files": "Files", "subtitleHeader": "Here's A Quick Overview Of Your Activity And Documents.", "sealedFiles": "Sealed Files", "assigned": "Assigned", "agencyActivityInsights": "Agency Activity Insights", "documentUploadsByTeam": "Document Uploads By Team", "sealedDocumentsPerClient": "Sealed Documents Per Client", "teamContributions": "Team Contributions", "lastMonth": "last month", "admin": "ADMIN", "finance": "FINANCE", "rashid": "RASHID", "goToMyIdeas": "Go To My Ideas", "foldersWithOngoingActivity": "folders with ongoing activity", "recentUploads": "Recent Uploads", "lastSealedIdeasOrFolders": "Last 5 sealed ideas or folders", "sealedAndTimestampedFiles": "sealed & timestamped files", "activeFolders": "Active Folders", "ideaTemplates": "Idea Templates", "draftFolders": "Draft Folders", "templatesForYourIdea": "Templates for your idea", "myIdeas": "My Ideas", "safeNewIdea": "Safe New Idea", "safeYourNewIdeaOrCreateFolder": "Safe your new idea or create a folder for your work", "ideaName": "Idea Name", "sedUtPerspiciatisUndeNisIsteNatus": "Sed ut perspiciatis unde nis iste natus", "sealedOnBlockchain": "Sealed on blockchain", "inDrafts": "In Drafts", "yourDocumentActivity": "Your Document Activity", "documentUploadsOverTime": "Document Uploads Over Time", "folderAndIdeaActivity": "Folder & Idea Activity", "uploaded": "Uploaded", "sealed": "Sealed", "newFolder": "New Folder", "uploads": "Uploads", "seals": "Seals", "sharedDocuments": "Shared Documents", "share_sub": "Track Your Recent Shared Files", "in_review_title": "In Review", "in_review_sub": "This are all folder in review", "sealed_title": "Sealed Folders", "sealed_sub": "Here are all sealed folders", "draft_title": "In Draft", "draft_sub": "Go to my draft folder", "in_review": "In Review"}, "clients": {"groovyLtd": "Groovy Ltd", "stripeInc": "Stripe Inc.", "appleInc": "Apple Inc."}, "team": {"chinKah": "<PERSON>", "peterQu": "<PERSON>", "adoniHira": "<PERSON><PERSON>"}, "selectRole": {"title": "Select Your Role", "subtitle": "Please enter your role", "individual_title": "I'm an Individual User", "individual_desc": "Perfect for users (Students, Private, Freelancer, Personal) “<PERSON> who want a personal digital copyright solution.", "individual_point1": "Manage your own projects", "individual_point2": "Safe your copyright ideas", "individual_point3": "Store your ideas for later use", "agency_title": "I represent an Agency/Team", "agency_desc": "Ideal for companies and agencies managing multiple clients or internal teams.", "agency_point1": "Centralised management for your organisation", "agency_point2": "Serve your own business or deliver services to clients", "agency_point3": "Advanced admin controls and collaboration features", "select_role_title": "The User Comes From Our Website www.swisstrustlayer.ch And Start His Journey Or Setup His Account", "copy_right": "Swiss copyright protections", "sealmyidea": "Sealmyidea", "footer_text": "© SwissTrustLayer"}, "login": {"user_title": "Welcome Back", "agency_title": "Agency Access", "agency_subtitle": "Secure access to manage and seal client ideas.", "user_subtitle": "Please enter your email and password for login.", "user_email": "Your Email", "email_placeholder": "Enter your email", "required_email": "Please input your email!", "valid_email": "Please enter a valid email!", "password": "Password", "required_password": "Please input your password!", "password_placeholder": "Enter your password", "remember_me": "Remember Me", "forgot_password": "Forgot password?", "login": "<PERSON><PERSON>", "dont_have_account": "Don't have an account yet?", "sign_up": "Sign up", "or": "or", "continue_with_google": "Continue with Google", "continue_with_apple": "Continue with Apple", "continue_with_facebook": "Continue with Facebook", "continue_with_linkedin": "Continue with LinkedIn", "agency_email_placeholder": "Enter agency email", "agency_name": "Agency or Company Name", "agency_name_required": "Please enter your agency name !", "agency_name_placeholder": "Enter agency name", "agency_email": "Agency Email"}, "forgotPassword": {"title": "Forgot Password", "subtitle": "Enter your email for the verification process, we", "subtitle1": "will send a 6-digit code to your email.", "email": "Your Email", "email_placeholder": "Your Email", "required_email": "Please enter your email!", "valid_email": "Please enter a valid email!", "send_code": "Send Code", "back_to_login": "Back to Login"}, "verifyOtp": {"title": "Enter Verification Code", "subtitle": "We've sent a 6-digit code to your email.", "subtitle1": "Please enter it below to continue.", "verify": "Verify", "resend_in": "Resend in", "sending": "Sending", "resend": "Resend", "back_to_login": "Back to Login", "2fa": "Please enter the 6-digit code from your authenticator app to continue."}, "newPassword": {"title": "New Password", "subtitle": "Your new password must be different from", "subtitle1": "previously used passwords.", "password": "Password", "required_password": "Please input your password!", "password_placeholder": "Enter your password", "new_password": "New Password", "required_confirm_password": "Please add your password!", "confirm_password_placeholder": "New password", "change_password": "Change Password", "cancel": "Cancel", "password_min_length": "Password must be at least 6 characters long", "password_pattern": "Password must contain at least one letter, one number, and one special character", "passwords_not_match": "Passwords do not match!"}, "checkInbox": {"title": "Check Your Inbox", "subtitle": "We've sent a verification link to:", "subtitle1": "Please click the link in the email to activate your account.", "resend": "Resend"}, "twoFactorAuthentication": {"title": "Two-Factor Authentication", "subTitle": "Scan the QR code using any authentication application on your phone(e.g. Google Authenticator, Duo Mobile, Authy) or enter the following code:", "activate_account": "Activate Account", "generating_qr_code": "Generating QR code..."}, "signup": {"title_user": "Create Your Account", "subtitle": "Secure your ideas in minutes with blockchain sealing.", "full_name": "Full Name", "full_name_placeholder": "Enter your full name", "required_full_name": "Please enter your full name!", "valid_full_name": "Full name should contain only letters and spaces", "email": "Email Address", "email_placeholder": "Enter your email", "required_email": "Please enter your email!", "valid_email": "Please enter a valid email!", "mobile_number": "Mobile Number", "mobile_number_placeholder": "Enter your mobile number", "valid_mobile_number": "Mobile number should contain only numbers", "password": "Password", "required_password": "Please input your password!", "password_placeholder": "Enter your password", "confirm_password": "Confirm Password", "required_confirm_password": "Please confirm your password!", "confirm_password_placeholder": "Confirm your password", "sign_up": "Sign Up", "agreement": "I agree to the Terms & Conditions", "already_account": "Already have an account?", "login": "<PERSON><PERSON>", "passwords_not_match": "Passwords do not match!", "agency_title": "Create Your Agency or Company Account", "agency_name": "Agency Name", "agency_name_placeholder": "Enter your agency name", "valid_agency_name": "Agency name should contain only letters and spaces"}, "passwordChanged": {"title": "Password Changed Successfully", "subtitle": "Your password has been changed successfully.", "subtitle1": "You can now log in with your new password.", "login": "<PERSON><PERSON>", "need_support": "Need Support"}, "passwordChangedFailed": {"title": "Password Change Failed", "subtitle": "Something went wrong while updating your password.", "subtitle1": "Please try again or contact support if the issue continues.", "try_again": "Try Again", "need_support": "Need Support"}, "settings": {"account_status": "Account Status", "saved_ideas": "Saved <PERSON>s", "my_submissions": "My Submissions", "2fa_authentication": "2FA Authentication", "account_type": "Account Type", "subscription_plan": "Subscription Plan", "activity_history": "Activity History", "upgrade_to_agency_account": "Upgrade To Agency Account", "active": "Active", "change_password": "Change Password", "transaction_history": "Transaction History"}, "pwa": {"install_title": "Install Seal my idea", "install_description": "Get the app for faster access and offline use", "install_button": "Install", "install_ios_instructions": "To install this app on your iOS device, tap the Share button and then \"Add to Home Screen\".", "install_android_instructions": "To install this app, open the browser menu and select \"Add to Home Screen\" or \"Install App\".", "install_general_instructions": "To install this app, look for the install icon in your browser's address bar or menu."}, "offline": {"title": "You're Offline", "subtitle": "It looks like you're not connected to the internet.", "subtitle2": "Please check your connection and try again.", "limited_features": "Some features may be limited while offline", "try_again": "Try Again", "works_offline": "This app works offline with limited functionality."}, "error": {"something_went_wrong": "Oops! Something went wrong", "error_message": "We're sorry, but something unexpected happened. Don't worry, you can try one of the options below to get back on track.", "try_again": "Try Again", "reload_page": "Reload Page", "reset_restart": "Reset & Restart", "resetting": "Resetting...", "technical_details": "Technical Details (for developers)", "error_label": "Error:", "component_stack": "Component Stack:"}, "sidebar": {"dashboard": "Dashboard", "safeNewIdea": "Safe New Idea", "myIdeas": "My Ideas", "sharingAndMember": "Sharing & Member", "subscriptionAndBilling": "Subscription & Billing", "notification": "Notification", "settings": "Settings", "user_management": "User Management", "analytics": "Analytics", "appearance_settings": "Appearance Settings", "log_out": "Log Out", "logoutPrompt": "Are you sure you want to logout?"}, "appearanceSettings": {"title": "Appearance Settings", "agencyCustomization": "Agency Customization", "subtitle": "Tailor The Platform To Fit Your Agency's Branding And Workflow.", "primaryColor": "Primary Color", "themeStyle": "Theme Style", "agencyLogo": "Agency Logo", "documentStyling": "Document Styling", "pdfSealBranding": "PDF Seal Branding", "watermarkSettings": "Watermark Settings", "themes": {"dark": "Dark", "light": "Light", "systemDefault": "System Default"}, "uploadLogo": "Upload Logo", "uploadCustomBranding": "Upload Custom Branding Preview", "addWatermark": "Add Watermark With Agency Name & Timestamp", "watermarkDescription": "Appears on all sealed documents automatically"}, "terms": {"title": "Terms & Conditions", "last_update": "last update 2/6/2025", "terms_condition": "Terms & Condition", "terms_description": "By creating an account, I confirm that I have read and agree to the Terms and Conditions.", "who_can_use": "Who can use this Service", "who_can_use_description": "This service is designed for both individuals and agencies.Individuals—such as professionals, freelancers, or anyone seeking personal digital solutions—can use the platform to streamline their own workflows and projects. Agencies and companies can leverage the service to manage their organization's needs or provide value-added solutions for their own clients. Whether you're working solo or representing a business, our platform adapts to your requirements", "copyright_protection": "Copyright Protection", "copyright_protection_description": "Individual and agency users retain full copyright over their creations on this platform. Your work is legally protected under copyright laws in the EU, Switzerland, the United Arab Emirates, and the USA. No content will be used, shared, or distributed without your explicit consent, ensuring your intellectual property rights remain secure and respected across all supported regions."}, "examples": {"antd_form_title": "Ant Design Form Example", "component_showcase": "Component Showcase", "antd_integration": "Ant Design Integration", "antd_integration_description": "This example shows how Ant Design components work seamlessly with styled-components and our existing Redux setup.", "button_variants": "Button Variants", "primary": "Primary", "default": "<PERSON><PERSON><PERSON>", "dashed": "Dashed", "text": "Text", "link": "Link", "custom_styled_button": "Custom Styled Button", "custom_gradient_button": "Custom Gradient Button", "typography": "Typography", "submit_another_form": "Submit Another Form", "reset": "Reset", "submit_form": "Submit Form", "information_saved": "Your information has been saved. Check the console for form data."}, "steps": {"tell_us": "Tell Us About Yourself", "select_goals": "Select Your Goals", "your_interests": "Your Interests", "how_did_you_hear": "How Did You Hear About Us?", "select_goals_subtitle": "Select multiple goals", "your_interests_subtitle": "We'll tailor an experience based on your", "your_interests_subtitle1": " interests, creating a personalized journey", "how_did_you_hear_subtitle": "Select one", "skip": "<PERSON><PERSON>", "next": "Next"}, "tell_about": {"gender": "Your Gender", "male": "Male", "female": "Female", "other": "Other", "birthday": "Your Birthday", "purpose": "Purpose of Joining", "enter_purpose": "Enter your purpose of joining", "enter_birthday": "Enter your birthday", "choose_gender": "Choose your gender"}, "hear_about": {"youtube": "Youtube", "twitter": "Twitter", "instagram": "Instagram", "facebook": "Facebook", "google": "Google", "other": "Other"}, "myIdeas": {"title": "My Ideas", "searchPlaceholder": "search ideas, tags...", "filters": {"allIdeas": "All Ideas", "recentIdeas": "Recent Ideas", "sealedDocuments": "Sealed Documents", "draft": "Draft", "inReview": "In Review", "sharedDocuments": "Shared Documents"}, "ideaName": "Idea Name", "ideaDescription": "Lorem ipsum dolor sit amet", "statusLabels": {"draft": "Draft", "sealed": "Sealed", "inReview": "In Review"}, "createNewFolder": "Create New Folder", "actions": {"edit": "Edit", "delete": "Delete", "open": "Open", "save": "Save"}, "deleteConfirmation": {"title": "Delete Folder", "message": "Are you sure you want to delete this folder? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel"}}, "createFolder": {"title": "Create A New Folder For An Idea", "fields": {"name": "Name", "phoneNumber": "Phone Number", "colors": "Colors", "description": "Description", "namePlaceholder": "Enter folder name", "phoneNumberPlaceholder": "Enter phone number", "descriptionPlaceholder": "Enter description"}, "actions": {"create": "Create", "cancel": "Cancel"}, "colorPicker": {"selectColor": "Select a color", "customColor": "Custom Color"}, "validation": {"nameRequired": "Folder name is required", "phoneRequired": "Phone number is required", "colorRequired": "Please select a color", "enter_valid_email": "Enter valid email", "pending": "Pending"}}, "draftIdea": {"status": "Status", "submittedOn": "Submitted On", "documentId": "Folder ID", "attachedFiles": "Attached Files", "upload_ideas": "Upload Ideas", "notes": "Notes", "ready_to_seal_prompt": "Ready To Seal Prompt", "ready_to_seal_prompt_description": "When you are done editing, seal your idea to lock and timestamp it.", "seal_my_idea_now": "Seal My Idea", "save_draft": "Save draft", "verification": "Verification", "download_proof": "Download Proof", "Download_proof_data": "Download Proof & Data", "seal_progress": "Seal Progress", "owner": "Owner", "review_started": "Review Started", "current_stage": "Current Stage", "awaiting_review": "Awaiting <PERSON> Approval", "downloading": "Downloading", "view_more": "View More", "more_files": "more files"}, "deleteIdea": {"download_ideas": "Download Ideas", "download": "Download", "deleteText1": "To permanently delete your content from the app, please confirm your request. All selected data will be removed from your app and cannot be recovered", "deleteText2": "I understand the information an would like to delete the project and the content inside", "cancel": "Cancel", "delete": "Delete Document", "seal_receipt": "Seal Receipt"}, "myProfile": {"title": "My Profile", "save_changes": "Save Changes", "name_label": "Name", "name_placeholder": "Enter your name", "email_label": "Email", "email_placeholder": "Enter your email", "profile_updated_success": "Profile updated successfully.", "profile_updated_description": "Your profile has been updated.", "profile_update_failed": "Profile update failed.", "profile_update_failed_description": "Please try again later.", "network_error": "Network error. Please try again.", "enter_valid_phone": "Enter a valid phone number", "change_password_failed": "Change password failed.", "password_updated_success": "Password updated successfully.", "password_updated_description": "Your password has been updated.", "valid_format": "Only JPG/PNG/WEBP format are allowed", "not_allowed_to_change_email": "Since you signed in with a social account, your email address cannot be changed here."}, "accountHistory": {"date_time": "Date & Time", "action": "Action", "description": "Description", "by_user": "By User", "sealed_document": "Sealed Document", "document_was_sealed": "\"Smart NDA Final.pdf\" was sealed.", "you": "You", "track_your_account": " Track all actions related to your account and submissions.", "search_activity": "Search Activity", "no_activity_found": "No activity found"}, "accountType": {"title": "Account Type", "subtitle": "Manage your current account type and access features tailored to your needs.", "account_type_label": "Account Type:", "individual_user": "Individual User", "member_since_label": "Member Since:", "march_2024": "March 2024", "plan_label": "Plan:", "basic_free_tier": "Basic (Free Tier", "usage_and_upgrade": "Usage And Upgrade Storage", "usage": "Usage", "per_month": "/Per Month", "basic": "Basic", "starter": "Starter", "professional": "Professional", "enterprise": "Enterprise", "basic_description": "Account Storage"}, "changePassword": {"title": "Change Password", "old_password_label": "Old Password", "old_password_placeholder": "Enter old password", "old_password_required": "Please enter your current password", "new_password_label": "New Password", "new_password_placeholder": "Enter new password", "new_password_required": "Please enter your new password", "confirm_password_label": "Confirm Password", "confirm_password_placeholder": "Confirm new password", "confirm_password_required": "Please confirm your new password", "passwords_not_match": "Passwords do not match!", "save_changes": "Save Changes"}, "header": {"greeting": "Hi! {name} 👋", "userManagement": "User Management", "subtitle": "Invite and manage users within your agency."}, "subscription": {"title": "Subscription Plan", "active_subscription": "Active Subscription", "basic_10mb": "Basic 10Mb", "price": "Price", "price_value": "Month", "billed_on": "Billed On", "july_1_2025": "July 1, 2025", "status": "Status", "active": "Active", "expired_on": "Expired On", "expired_description": "After 360 Day's With No Action On The Account", "subscribeMonth": "Subscribe by Month", "subscribeYearly": "Upgrade by Year and get 25% off", "downgrade": "Downgrade to", "upgradeTo": "Upgrade To", "storage_limit": "Storage Limit", "monthly": "Monthly", "yearly": "Yearly", "subscribeMonthly": "Downgrade by Monthly", "upgradeToYearly": "Upgrade to Yearly (25% off)"}, "subscriptionPlan": {"title": "Choose Your Plan", "basic": "Basic", "professional": "Professional", "per_month": "/Per Month", "per_year": "/Per Year", "basic_description": "Account Storage.", "plan_active_title": "Your {planName} plan is active", "plan_active_desc": "Your subscription is active and will auto-renew on {endDate}. We will automatically charge your saved payment method for the next {interval} billing cycle.", "plan_cancelled_title": "Your {planName} plan is ending", "plan_cancelled_desc": "Your subscription will end on {endDate} and will not renew. You won't be charged again."}, "view": {"title": "Upload Files", "subTitle": "Select documents you want to seal", "selectFile": "Please select files to upload.", "drop_files": "Drop Files Here", "supported_formats": "Supported formats: PNG, JPG, PDF, DOCX", "or": "OR", "browse": "Browse File", "upload": "Upload", "cancel": "Cancel"}, "certificate": {"certificate_verification_failed": "Certificate verification failed", "no_certificate_found": "No Certificate Found"}, "myIdeasTabs": {"myIdea": "My Idea", "allFiles": "All Files", "sharing": "Sharing", "removeFileConfirmation": "Are you sure you want to remove this file?", "fileDeleteSuccess": "File deleted", "fileDeleteSuccessMessage": "File deleted successfully", "fileDeleteFailed": "File deletion failed.", "acesssRemoved": "Access Removed", "acesssRemovedSuccess": "Access removed successfully."}, "readyToSeal": {"subTitle": "Select documents you want to seal", "view_more": "View More", "viewLess": "View Less", "more_files": "more files", "seal_method": "Seal Copyright method", "one_time_cost": "One Time Cost", "seal_now": "Seal My Idea Now", "cancel": "Cancel"}, "sealProcessDone": {"title": "Seal Process Done", "text": "Your idea is now protected by a verified Time Stamp", "button": "Go To My Ideas"}, "twoFAModal": {"title": "Two Factor Authentication", "subTitle": "  Please enter the 6-digit code from your authenticator app to continue.", "verify": "Verify"}, "paymentStatus": {"title": "Payment", "successText": "Order Placed Successfully", "failedText": "Payment Failed", "subTotal": "Subtotal", "price": "Price", "billedOn": "Billed On", "card": "Card", "total": "Total", "receipt": "Receipt", "try_again": "Try Again", "processTitle": "Payment is in Process", "subtitle": "Please wait while we confirm your payment…", "selectMethod": "Please select a copy right method"}, "receipt": {"title": "Receipt", "date": "Date", "amount": "Amount", "plan": "Plan", "invoice": "Invoice", "expired_on": "Expired On", "download_pdf": "Download PDF", "back_to_idea": "Back To Idea", "back_to_subscription": "Back To Subscription", "next_billing_date": "Next Billing Date"}, "notification": {"turn_on_notification": "Turn On Notifications", "stay_up_to_date": "Make sure you stay up-to-date with all the important news and reminders by enabling notifications. You can always turn it off later.", "enable_notification": "Enable Notifications", "maybe_later": "Maybe Later", "sure_delete_all_notification": "Are you sure you want to delete all notification?", "notficationsDeleted": "All notifications deleted successfully", "notification_deleted": "Notifications deleted", "no_notifications_found": "No Notifications Found", "notification_permission_changed": "Notification Permission Changed", "invite_accepted_title": "Invitation Accepted", "invite_accepted_description": "You have successfully joined the shared folder.", "invite_accept_failed_title": "Invitation Acceptance Failed", "invite_accept_failed_description": "We couldn't accept the invitation. Please try again later."}, "roles": {"user": "User", "legal_user": "Legal User", "admin": "Admin"}, "inviteModal": {"title": "Invite Member", "select_role": "Select Role", "share_link": "Share Your Link", "invite_friends": "Invite Members", "email_placeholder": "Enter email address", "send_invite": "Send Invite", "update_invite": "Update Invite", "select_folder": "Select Folder", "folder_placeholder": "Select a folder", "select_folder_required": "Please select a folder"}, "emailVerification": {"title": "Email Verification", "subtitle": "We've sent a 6-digit code to your email. Please enter it below to continue.", "verify": "Verify"}, "users": {"createNewUser": "New Member", "member": "Member", "invitation_sent_title": "Invitation Sen<PERSON>", "invitation_sent_description": "The invitation has been sent to the user.", "invitation_failed_title": "Invitation Failed", "invitation_failed_description": "Failed to send the invitation. Please try again later.", "confirm_remove_access_user": "Are you sure you want to remove folder access from this user?"}, "reset2FA": {"title": "Two-Factor Authentication", "subtitle": "Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in", "twoFA": "Two-factor method", "auth_app": "Authenticator App", "subtitle1": "Use an authentication app or browser extension to get two-factor authentication codes when prompted"}, "userManagement": {"title": "Search Users & Ideas", "searchPlaceholder": "Search by name and email...", "rolePlaceholder": "All roles", "invite": "Invite Users", "removeUserText": "Removing this user will also remove them from all shared folders.", "confirmRemoveAccessUser": "Are you sure you want to remove folder access from this user?", "cancel": "Cancel", "remove": "Remove", "usernameEmail": "Username & Email", "joinedFolder": "Joined Folder", "action": "Action"}, "transactions": {"no_transactions_found": "No transactions found", "search": "Search transactions", "history": "Transactions History", "track_all_history": "Track all transactions related subscription plans and sealed ideas payments."}, "userManagementDetails": {"cancel": "Cancel", "revoke": "Revoke", "confirmRemoveAccessUser": "Are you sure you want to remove folder access from this user?"}}